#!/usr/bin/env python3
"""
Test script to run the enhanced Advanced Slippage UI
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from flask import Flask, render_template, jsonify, request
from datetime import datetime

app = Flask(__name__, 
           template_folder='app/templates',
           static_folder='app/static')

# Dummy data generator for testing
def generate_dummy_slippage_data():
    """Generate dummy slippage data for testing"""
    return {
        'total_slippage': -45230,
        'execution_slippage': 23450,
        'avg_execution_time': 2.34,
        'total_turnover': 125000000,  # 12.5 Cr
        'total_trades': 1247,
        'slip_to_turnover': -18.5,
        'slippage_change': -12.5,
        'exec_slippage_change': 8.3,
        'exec_time_change': -0.2,
        'turnover_change': 15.2,
        'trades_change': 23,
        'ratio_change': -2.1
    }

def generate_chart_data():
    """Generate dummy chart data"""
    dates = [(datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(30, 0, -1)]
    
    return {
        'dates': dates,
        'strategies': {
            'strategy_alpha_v1': [random.randint(-5000, 5000) for _ in dates],
            'strategy_beta_v2': [random.randint(-3000, 7000) for _ in dates],
            'strategy_gamma_v1': [random.randint(-4000, 6000) for _ in dates]
        },
        'slaves': {
            'slave_001': [random.randint(-2000, 4000) for _ in dates],
            'slave_002': [random.randint(-3000, 5000) for _ in dates],
            'slave_hedge_001': [random.randint(-1000, 3000) for _ in dates]
        }
    }

@app.route('/')
def index():
    """Redirect to advanced slippage dashboard"""
    return render_template('slip_dashboard/advanced_slippage.html')

@app.route('/advanced-slippage')
def advanced_slippage():
    """Enhanced Advanced Slippage Dashboard"""
    return render_template('slip_dashboard/advanced_slippage.html')

@app.route('/api/slippage-summary')
def slippage_summary():
    """API endpoint for slippage summary data"""
    return jsonify(generate_dummy_slippage_data())

@app.route('/api/chart-data')
def chart_data():
    """API endpoint for chart data"""
    chart_type = request.args.get('type', 'trend')
    return jsonify(generate_chart_data())

@app.route('/api/strategies')
def get_strategies():
    """API endpoint for available strategies"""
    return jsonify([
        {'value': 'strategy_alpha_v1', 'label': 'strategy_alpha_v1'},
        {'value': 'strategy_beta_v2', 'label': 'strategy_beta_v2'},
        {'value': 'strategy_gamma_v1', 'label': 'strategy_gamma_v1'},
        {'value': 'cluster_izmir', 'label': 'cluster_izmir'},
        {'value': 'cluster_kari', 'label': 'cluster_kari'}
    ])

@app.route('/api/slaves')
def get_slaves():
    """API endpoint for available slaves"""
    return jsonify([
        {'value': 'slave_001', 'label': 'slave_001'},
        {'value': 'slave_002', 'label': 'slave_002'},
        {'value': 'slave_003', 'label': 'slave_003'},
        {'value': 'slave_hedge_001', 'label': 'slave_hedge_001'},
        {'value': 'slave_hedge_002', 'label': 'slave_hedge_002'}
    ])

if __name__ == '__main__':
    print("Starting Enhanced Advanced Slippage UI Test Server...")
    print("Dashboard will be available at: http://localhost:5000")
    print("Features included:")
    print("   - Enhanced multi-select dropdowns")
    print("   - Strategy-slave mapping")
    print("   - Improved chart containers with legend controls")
    print("   - Enhanced filter panel with quick dates")
    print("   - Dummy data for testing functionality")
    print("\nTest the following features:")
    print("   1. Multi-select dropdowns with search")
    print("   2. Strategy selection -> Load Slaves functionality")
    print("   3. Quick date buttons")
    print("   4. Enhanced chart layouts")
    print("   5. Tab navigation")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
