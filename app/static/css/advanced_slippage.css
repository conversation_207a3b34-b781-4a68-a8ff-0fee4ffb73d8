/* Advanced Slippage Dashboard Styles */
/* Following SAMBA design patterns with primary colors: #4d0000, #c50000, #D52027 */

:root {
    --samba-dark-red: #4d0000;
    --samba-red: #c50000;
    --samba-light-red: #D52027;
    --background-white: #ffffff;
    --background-light: #f8f9fa;
    --border-light: #e9ecef;
    --text-dark: #2f4251;
    --text-muted: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--samba-dark-red), var(--samba-red));
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.page-header h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
}

.page-header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

/* Dashboard Summary */
.dashboard-summary {
    margin-bottom: 25px;
}

.summary-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.summary-metric {
    background: var(--background-white);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    border: 1px solid var(--border-light);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-metric:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.metric-value.positive { color: var(--success-color); }
.metric-value.negative { color: var(--danger-color); }
.metric-value.neutral { color: var(--samba-dark-red); }

.metric-label {
    font-size: 12px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.metric-change {
    font-size: 11px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 12px;
}

.metric-change.positive {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.metric-change.negative {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.metric-change.neutral {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.metric-change.positive::before {
    content: "↗ ";
}

.metric-change.negative::before {
    content: "↘ ";
}

.metric-change.neutral::before {
    content: "→ ";
}

/* Metric Animation Classes */
.metric-updating {
    position: relative;
    overflow: hidden;
}

.metric-updating::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(196, 0, 0, 0.3), transparent);
    animation: shimmer 1s ease-in-out;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.change-flash {
    animation: changeFlash 0.6s ease-in-out;
}

@keyframes changeFlash {
    0% {
        background: rgba(196, 0, 0, 0.2);
        transform: scale(1);
    }
    50% {
        background: rgba(196, 0, 0, 0.4);
        transform: scale(1.05);
    }
    100% {
        background: transparent;
        transform: scale(1);
    }
}

.metrics-updated {
    animation: metricsPulse 1s ease-in-out;
}

@keyframes metricsPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(196, 0, 0, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(196, 0, 0, 0.1);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(196, 0, 0, 0);
    }
}

/* Enhanced metric hover effects */
.summary-metric:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.summary-metric:hover .metric-value {
    color: var(--samba-red);
}

/* Real-time update indicators */
.metric-value.updating {
    color: var(--warning-color);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Tab Navigation */
.nav-tabs {
    border-bottom: 2px solid var(--samba-red);
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    color: var(--text-dark);
    font-weight: 500;
    padding: 12px 20px;
    border-radius: 8px 8px 0 0;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background: rgba(196, 0, 0, 0.1);
    color: var(--samba-red);
}

.nav-tabs .nav-link.active {
    background: var(--samba-red);
    color: white;
    border-bottom: 2px solid var(--samba-red);
}

/* Filter Panel */
.filter-panel {
    background: var(--background-white);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.filter-panel h5 {
    color: var(--samba-dark-red);
    margin-bottom: 15px;
    font-weight: 600;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: end;
    margin-bottom: 15px;
}

.filter-row:last-child {
    margin-bottom: 0;
}

.filter-group {
    flex: 1;
    min-width: 150px;
}

.filter-group.date-range {
    min-width: 250px;
}

.form-label {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 5px;
    font-size: 13px;
}

.form-control, .form-select {
    border: 1px solid var(--border-light);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--samba-red);
    box-shadow: 0 0 0 0.2rem rgba(196, 0, 0, 0.25);
}

/* Quick Dates */
.quick-dates {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-date-btn {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.quick-date-btn:hover {
    background: var(--samba-red);
    border-color: var(--samba-red);
    color: white;
}

.quick-date-btn.active {
    background: var(--samba-red);
    border-color: var(--samba-red);
    color: white;
}

/* Enhanced Multi-Select Dropdown Styles */
.multi-select-wrapper {
    position: relative;
    width: 100%;
}

.multi-select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    background: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    min-height: 38px;
}

.multi-select-trigger:hover {
    border-color: var(--samba-red);
}

.multi-select-trigger.active {
    border-color: var(--samba-red);
    box-shadow: 0 0 0 0.2rem rgba(196, 0, 0, 0.25);
}

.multi-select-display {
    flex: 1;
    color: var(--text-dark);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.multi-select-count {
    background: var(--samba-red);
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 11px;
    font-weight: 500;
    margin-left: 8px;
}

.multi-select-arrow {
    color: var(--text-muted);
    transition: transform 0.3s ease;
    margin-left: 8px;
}

.multi-select-trigger.active .multi-select-arrow {
    transform: rotate(180deg);
}

.multi-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-light);
    border-top: none;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow: hidden;
}

.multi-select-dropdown.show {
    display: block;
}

.multi-select-header {
    padding: 10px;
    border-bottom: 1px solid var(--border-light);
    background: var(--background-light);
}

.multi-select-search {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid var(--border-light);
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 8px;
}

.multi-select-actions {
    display: flex;
    gap: 8px;
}

.multi-select-action-btn {
    padding: 4px 8px;
    border: 1px solid var(--border-light);
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.multi-select-action-btn:hover {
    background: var(--samba-red);
    color: white;
    border-color: var(--samba-red);
}

.multi-select-options {
    max-height: 200px;
    overflow-y: auto;
}

.multi-select-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid var(--background-light);
}

.multi-select-option:hover {
    background: var(--background-light);
}

.multi-select-option.selected {
    background: rgba(196, 0, 0, 0.1);
}

.multi-select-checkbox {
    margin-right: 8px;
    cursor: pointer;
}

.multi-select-label {
    flex: 1;
    font-size: 13px;
    color: var(--text-dark);
}

/* Enhanced Filter Group Styles */
.filter-group.quick-dates-group {
    min-width: 300px;
}

.filter-group.slave-group {
    min-width: 250px;
}

.filter-group.actions {
    min-width: 200px;
}

/* Enhanced Chart Containers with Better Space Utilization */
.chart-container {
    background: var(--background-white);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
    min-height: 400px;
    border: 1px solid var(--border-light);
    position: relative;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chart-container.compact {
    min-height: 320px;
    padding: 8px;
}

.chart-container.large {
    min-height: 500px;
}

.chart-container > div[id$="-chart"] {
    width: 100% !important;
    flex: 1;
    min-height: 350px;
    max-width: 100%;
    overflow: visible;
    display: block;
}

.chart-container.compact > div[id$="-chart"] {
    min-height: 280px;
}

.chart-container.large > div[id$="-chart"] {
    min-height: 450px;
}

/* Enhanced Plotly chart responsiveness */
.chart-container .js-plotly-plot {
    width: 100% !important;
    height: 100% !important;
    min-height: inherit;
}

.chart-container .plotly {
    width: 100% !important;
    height: 100% !important;
    min-height: inherit;
}

.chart-container .plotly .main-svg {
    width: 100% !important;
    height: 100% !important;
}

/* Legend positioning options */
.chart-container .plotly .legend {
    max-height: 120px;
    overflow-y: auto;
}

.chart-container.legend-bottom .plotly .legend {
    y: -0.2;
}

.chart-container.legend-right .plotly .legend {
    x: 1.02;
    y: 1;
}

.chart-container.legend-top .plotly .legend {
    y: 1.1;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
    flex-shrink: 0;
    padding: 5px 0;
}

.chart-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    flex: 1;
}

.chart-controls {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
    flex-shrink: 0;
}

.chart-legend-controls {
    display: flex;
    gap: 4px;
    align-items: center;
}

.legend-position-btn {
    padding: 3px 6px;
    border: 1px solid var(--border-light);
    background: white;
    border-radius: 3px;
    cursor: pointer;
    font-size: 10px;
    color: var(--text-muted);
    transition: all 0.2s ease;
}

.legend-position-btn.active {
    background-color: var(--samba-red);
    color: white;
    border-color: var(--samba-red);
}

.legend-position-btn:hover:not(.active) {
    background-color: var(--background-light);
    color: var(--samba-red);
}

.chart-type-toggle {
    display: flex;
    gap: 2px;
    border: 1px solid var(--border-light);
    border-radius: 4px;
    overflow: hidden;
}

.chart-type-btn {
    padding: 4px 8px;
    border: none;
    background: white;
    cursor: pointer;
    font-size: 11px;
    color: var(--text-dark);
    transition: all 0.2s ease;
}

.chart-type-btn.active {
    background: var(--samba-red);
    color: white;
}

.chart-type-btn:hover:not(.active) {
    background: var(--background-light);
    color: var(--samba-red);
}

/* Multi-Select Dropdowns */
.multi-select-wrapper {
    position: relative;
    width: 100%;
}

.multi-select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    background: var(--background-white);
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 38px;
    position: relative;
}

.multi-select-trigger:hover {
    border-color: var(--samba-red);
    box-shadow: 0 2px 4px rgba(196, 0, 0, 0.1);
}

.multi-select-trigger:focus-within,
.multi-select-trigger.active {
    border-color: var(--samba-red);
    box-shadow: 0 0 0 0.2rem rgba(196, 0, 0, 0.25);
}

.multi-select-trigger.has-selections {
    background: linear-gradient(135deg, rgba(196, 0, 0, 0.05), rgba(77, 0, 0, 0.05));
}

.multi-select-display {
    flex: 1;
    font-size: 14px;
    color: var(--text-dark);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.multi-select-count {
    background: var(--samba-red);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.multi-select-arrow {
    margin-left: 8px;
    color: var(--text-muted);
    transition: transform 0.3s ease;
}

.multi-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--background-white);
    border: 1px solid var(--border-light);
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 300px;
    overflow: hidden;
}

.multi-select-header {
    padding: 10px;
    border-bottom: 1px solid var(--border-light);
    background: var(--background-light);
}

.multi-select-search {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid var(--border-light);
    border-radius: 4px;
    font-size: 13px;
    margin-bottom: 8px;
}

.multi-select-actions {
    display: flex;
    gap: 8px;
}

.multi-select-action-btn {
    background: none;
    border: none;
    color: var(--samba-red);
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.multi-select-action-btn:hover {
    background: rgba(196, 0, 0, 0.1);
}

.multi-select-options {
    max-height: 200px;
    overflow-y: auto;
}

.multi-select-option {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin: 2px 4px;
}

.multi-select-option:hover {
    background: var(--background-light);
    transform: translateX(2px);
}

.multi-select-option.selected {
    background: rgba(196, 0, 0, 0.1);
    border-left: 3px solid var(--samba-red);
}

.multi-select-checkbox {
    margin-right: 10px;
    cursor: pointer;
    width: 16px;
    height: 16px;
    accent-color: var(--samba-red);
}

.multi-select-label {
    flex: 1;
    font-size: 14px;
    color: var(--text-dark);
    font-weight: 500;
}

.multi-select-option.selected .multi-select-label {
    color: var(--samba-dark-red);
    font-weight: 600;
}

.multi-select-option.keyboard-focus {
    background: var(--samba-red);
    color: white;
    outline: 2px solid var(--samba-dark-red);
    outline-offset: -2px;
}

.multi-select-option.keyboard-focus .multi-select-label {
    color: white;
}

/* No results message styling */
.no-results-message {
    padding: 10px;
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    border-top: 1px solid var(--border-light);
    margin-top: 5px;
}

/* Buttons */
.btn-primary {
    background: var(--samba-red);
    border-color: var(--samba-red);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--samba-dark-red);
    border-color: var(--samba-dark-red);
}

.btn-secondary {
    background: var(--text-muted);
    border-color: var(--text-muted);
    font-weight: 500;
}

/* Enhanced Loading Indicators */
.loading-indicator {
    text-align: center;
    padding: 60px 40px;
    background: var(--background-white);
    border-radius: 12px;
    border: 1px solid var(--border-light);
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.loading-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(196, 0, 0, 0.1), transparent);
    animation: loadingShimmer 2s infinite;
}

@keyframes loadingShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--samba-red);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
    position: relative;
    z-index: 2;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--samba-dark-red);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    position: relative;
    z-index: 2;
}

.loading-subtext {
    color: var(--text-muted);
    font-size: 14px;
    font-style: italic;
    position: relative;
    z-index: 2;
}

/* Advanced Loading Spinner */
.advanced-spinner {
    display: inline-block;
    width: 60px;
    height: 60px;
    position: relative;
    margin: 0 auto 20px;
}

.advanced-spinner::before,
.advanced-spinner::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: var(--samba-red);
    animation: advancedSpin 1.5s linear infinite;
}

.advanced-spinner::before {
    width: 60px;
    height: 60px;
    top: 0;
    left: 0;
}

.advanced-spinner::after {
    width: 40px;
    height: 40px;
    top: 10px;
    left: 10px;
    border-top-color: var(--samba-light-red);
    animation-duration: 1s;
    animation-direction: reverse;
}

@keyframes advancedSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 16px;
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-text.medium {
    width: 80%;
}

.skeleton-text.long {
    width: 100%;
}

.skeleton-card {
    height: 120px;
    border-radius: 8px;
    margin-bottom: 16px;
}

.skeleton-chart {
    height: 300px;
    border-radius: 8px;
    margin-bottom: 20px;
}

/* Loading States for Components */
.component-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.component-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.component-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--samba-red);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1001;
}

/* Chart Error State */
.chart-error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    background: var(--background-light);
    border-radius: 8px;
    border: 2px dashed var(--border-light);
    min-height: 300px;
}

.chart-error-state .error-icon {
    font-size: 48px;
    color: var(--warning-color);
    margin-bottom: 20px;
}

.chart-error-state .error-message {
    color: var(--text-muted);
    font-size: 16px;
    margin-bottom: 20px;
    max-width: 300px;
}

.chart-error-state .retry-chart-btn {
    transition: all 0.3s ease;
}

.chart-error-state .retry-chart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Fade-in animations for content */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

@keyframes scaleIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

/* Staggered animations */
.stagger-animation {
    opacity: 0;
    transform: translateY(20px);
    animation: staggerFadeIn 0.6s ease forwards;
}

.stagger-animation:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation:nth-child(5) { animation-delay: 0.5s; }

@keyframes staggerFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Content Cards */
.content-card {
    background: var(--background-white);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.content-card h4 {
    color: var(--samba-dark-red);
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 18px;
}

/* Chart Containers */
.chart-container {
    background: var(--background-white);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid var(--border-light);
    min-height: 450px;
    display: block;
    position: relative;
    clear: both;
    width: 100%;
    box-sizing: border-box;
}

.chart-container > div[id$="-chart"] {
    width: 100% !important;
    height: 400px !important;
    min-height: 400px !important;
    max-width: 100% !important;
    flex: 1;
    overflow: hidden;
    position: relative;
}

.chart-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: var(--background-light);
    border: 2px dashed var(--border-light);
    border-radius: 8px;
    width: 100%;
    position: relative;
}

.chart-placeholder-content {
    text-align: center;
    color: var(--text-muted);
}

.chart-placeholder-content i {
    margin-bottom: 15px;
    opacity: 0.5;
}

.chart-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-size: 16px;
}

/* Enhanced Plotly chart fixes */
.chart-container .js-plotly-plot {
    width: 100% !important;
    height: 100% !important;
}

.chart-container .plotly {
    width: 100% !important;
    height: 100% !important;
}

.chart-container .plotly .main-svg {
    width: 100% !important;
    height: 100% !important;
}

.chart-container .plotly-graph-div {
    width: 100% !important;
    height: 100% !important;
}

.chart-container .svg-container {
    width: 100% !important;
    height: 100% !important;
}

/* Fix text overlap and sizing issues */
.chart-container .plotly .xtick text,
.chart-container .plotly .ytick text {
    font-size: 10px !important;
    font-family: 'Raleway', sans-serif !important;
}

.chart-container .plotly .legend text {
    font-size: 11px !important;
    font-family: 'Raleway', sans-serif !important;
}

.chart-container .plotly .gtitle text {
    font-size: 14px !important;
    font-weight: 600 !important;
    font-family: 'Raleway', sans-serif !important;
}

.chart-container .plotly .xaxislayer-above text,
.chart-container .plotly .yaxislayer-above text {
    font-size: 10px !important;
}

/* Ensure proper chart responsiveness */
.chart-container .plotly .modebar {
    right: 10px !important;
    top: 10px !important;
}

.chart-container .plotly .modebar-btn {
    width: 22px !important;
    height: 22px !important;
}

/* Fix for floating elements */
.content-card {
    clear: both;
    width: 100%;
    margin-bottom: 25px;
    position: relative;
    display: block;
}

.content-card h4 {
    margin-bottom: 20px;
    clear: both;
}

.charts-container {
    width: 100%;
    clear: both;
}

.charts-container .content-card {
    margin-bottom: 30px;
    float: none;
    clear: both;
}

/* Enhanced Data Tables */
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
    clear: both;
}

.table-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: end;
}

.table-control-group {
    display: flex;
    flex-direction: column;
    min-width: 120px;
}

.table-control-group .form-label {
    margin-bottom: 3px;
    font-size: 11px;
    font-weight: 500;
    color: var(--text-muted);
}

.form-select-sm, .form-control-sm {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
}

.btn-group .btn {
    font-size: 12px;
    padding: 6px 10px;
}

/* Summary Statistics Row */
.summary-stats-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 15px;
    background: var(--background-light);
    border-radius: 6px;
    margin-bottom: 15px;
    border: 1px solid var(--border-light);
}

.summary-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120px;
}

.stat-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 3px;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--samba-dark-red);
}

/* Enhanced Data Tables */
.table-responsive {
    border-radius: 8px;
    border: 1px solid var(--border-light);
    max-height: 600px;
    overflow-y: auto;
}

.table {
    margin-bottom: 0;
}

.table-dark th {
    background: var(--samba-dark-red);
    color: white;
    font-weight: 600;
    font-size: 13px;
    border-bottom: 2px solid var(--samba-red);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s ease;
}

.table th.sortable:hover {
    background: var(--samba-red);
}

.table th.sortable i {
    margin-left: 5px;
    opacity: 0.6;
}

.table th.sortable.sorted-asc i:before {
    content: "\f0de"; /* fa-sort-up */
}

.table th.sortable.sorted-desc i:before {
    content: "\f0dd"; /* fa-sort-down */
}

.table td {
    font-size: 14px;
    vertical-align: middle;
    padding: 10px 12px;
    position: relative;
}

.table-hover tbody tr:hover,
.table tbody tr.row-hover {
    background: rgba(196, 0, 0, 0.05);
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Enhanced table cell styling */
.table-row {
    cursor: pointer;
    transition: all 0.3s ease;
}

.strategy-cell, .slave-cell {
    max-width: 150px;
}

.strategy-name, .slave-name {
    font-weight: 500;
    color: var(--text-dark);
}

.date-cell {
    min-width: 80px;
}

.date-value {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--text-muted);
}

/* Slippage cell with visual indicators */
.slippage-cell {
    position: relative;
    min-width: 120px;
}

.slippage-value {
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.slippage-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--samba-red), var(--samba-light-red));
    border-radius: 2px;
    opacity: 0.6;
    transition: width 0.5s ease;
}

.high-slippage .slippage-value {
    color: var(--danger-color);
    font-weight: 700;
}

.medium-slippage .slippage-value {
    color: var(--warning-color);
    font-weight: 600;
}

.low-slippage .slippage-value {
    color: var(--success-color);
}

.negative-slippage .slippage-value {
    color: var(--info-color);
    font-weight: 600;
}

.high-slippage .slippage-bar {
    background: linear-gradient(90deg, var(--danger-color), #ff6b6b);
}

.medium-slippage .slippage-bar {
    background: linear-gradient(90deg, var(--warning-color), #ffd93d);
}

.low-slippage .slippage-bar {
    background: linear-gradient(90deg, var(--success-color), #51cf66);
}

.negative-slippage .slippage-bar {
    background: linear-gradient(90deg, var(--info-color), #339af0);
}

/* Trade count cell with indicator */
.trade-count-cell {
    position: relative;
    min-width: 100px;
}

.trade-count-value {
    font-weight: 500;
    position: relative;
    z-index: 2;
}

.trade-count-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--info-color), #74c0fc);
    border-radius: 1px;
    opacity: 0.5;
    transition: width 0.5s ease;
}

/* Ratio cell styling */
.ratio-cell {
    min-width: 100px;
}

.ratio-value {
    font-weight: 600;
    color: var(--text-dark);
}

.ratio-unit {
    font-size: 11px;
    color: var(--text-muted);
    margin-left: 2px;
}

/* Row details modal */
.row-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    animation: modalFadeIn 0.3s ease;
}

.row-details-modal .modal-content {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

.row-details-modal .modal-header {
    background: linear-gradient(135deg, var(--samba-dark-red), var(--samba-red));
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.row-details-modal .modal-header h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.row-details-modal .modal-body {
    padding: 25px;
    max-height: 60vh;
    overflow-y: auto;
}

.row-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: var(--background-light);
    border-radius: 6px;
    border-left: 3px solid var(--samba-red);
}

.detail-item label {
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    font-size: 13px;
}

.detail-item span {
    font-weight: 500;
    color: var(--text-muted);
    text-align: right;
    font-size: 14px;
}

/* Export Progress Modal */
.export-progress-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10002;
    animation: modalFadeIn 0.3s ease;
}

.export-progress-modal .modal-content {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    padding: 0;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

.export-progress-content {
    padding: 40px;
    text-align: center;
    min-width: 300px;
}

.export-progress-content .spinner {
    width: 50px;
    height: 50px;
    border: 5px solid var(--border-light);
    border-top: 5px solid var(--samba-red);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.export-progress-content h5 {
    color: var(--samba-dark-red);
    margin-bottom: 10px;
    font-weight: 600;
}

.export-progress-content p {
    color: var(--text-muted);
    margin-bottom: 20px;
    font-size: 14px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--border-light);
    border-radius: 3px;
    overflow: hidden;
    margin-top: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--samba-red), var(--samba-light-red));
    width: 0%;
    transition: width 1s ease;
    border-radius: 3px;
}

/* Table Pagination */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-top: 1px solid var(--border-light);
    margin-top: 15px;
}

.pagination-info {
    font-size: 13px;
    color: var(--text-muted);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-controls .btn {
    font-size: 12px;
    padding: 6px 12px;
}

.pagination-controls .form-select {
    width: auto;
    min-width: 120px;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

/* Comparison Features */
.comparison-kpis {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 25px;
}

.kpi-section {
    flex: 1;
    min-width: 300px;
    padding: 20px;
    background: var(--background-light);
    border-radius: 8px;
    border: 1px solid var(--border-light);
}

.kpi-section h6 {
    color: var(--samba-dark-red);
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-pills .nav-link {
    color: var(--text-dark);
    background: var(--background-light);
    border: 1px solid var(--border-light);
    margin-right: 5px;
    font-size: 13px;
    padding: 8px 15px;
    transition: all 0.3s ease;
}

.nav-pills .nav-link:hover {
    background: rgba(196, 0, 0, 0.1);
    color: var(--samba-red);
}

.nav-pills .nav-link.active {
    background: var(--samba-red);
    color: white;
    border-color: var(--samba-red);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.chart-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.chart-controls .btn {
    font-size: 12px;
    padding: 6px 10px;
}

.chart-controls .form-select {
    font-size: 12px;
    padding: 4px 8px;
    width: auto;
    min-width: 100px;
}

.performance-summary-container {
    margin-top: 15px;
}

.performance-insights {
    background: var(--background-light);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-light);
    height: fit-content;
}

.performance-insights h6 {
    color: var(--samba-dark-red);
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 14px;
}

.insight-item {
    padding: 10px;
    margin-bottom: 10px;
    background: var(--background-white);
    border-radius: 6px;
    border-left: 4px solid var(--samba-red);
    font-size: 13px;
}

.insight-item.positive {
    border-left-color: var(--success-color);
}

.insight-item.negative {
    border-left-color: var(--danger-color);
}

.insight-item.neutral {
    border-left-color: var(--info-color);
}

.analysis-controls {
    background: var(--background-light);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--border-light);
}

.detailed-analysis-results {
    min-height: 200px;
    padding: 20px;
    background: var(--background-white);
    border-radius: 8px;
    border: 1px solid var(--border-light);
}

.comparison-metric-card {
    background: var(--background-white);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.comparison-metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.comparison-metric-value {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 5px;
}

.comparison-metric-value.positive { color: var(--success-color); }
.comparison-metric-value.negative { color: var(--danger-color); }
.comparison-metric-value.neutral { color: var(--samba-dark-red); }

.comparison-metric-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.comparison-metric-change {
    font-size: 10px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 10px;
}

.comparison-metric-change.positive {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.comparison-metric-change.negative {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.comparison-metric-change.neutral {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

/* Performance Insights */
.performance-insights {
    background: var(--background-light);
    border-radius: 8px;
    padding: 20px;
    height: fit-content;
}

.performance-insights h6 {
    color: var(--samba-dark-red);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.insight-item {
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.4;
    border-left: 4px solid;
}

.insight-item.positive {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: var(--success-color);
    color: #155724;
}

.insight-item.negative {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
    color: #721c24;
}

.insight-item.neutral {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: var(--info-color);
    color: #0c5460;
}

/* Analysis Stats */
.analysis-summary {
    background: var(--background-light);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.analysis-stat {
    text-align: center;
    padding: 15px;
    background: var(--background-white);
    border-radius: 6px;
    border: 1px solid var(--border-light);
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--samba-dark-red);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
}

/* Enhanced Toast Notifications */
.toast-container {
    position: fixed;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 420px;
    pointer-events: none;
}

.toast-container.toast-top-right {
    top: 20px;
    right: 20px;
}

.toast-container.toast-top-left {
    top: 20px;
    left: 20px;
}

.toast-container.toast-bottom-right {
    bottom: 20px;
    right: 20px;
}

.toast-container.toast-bottom-left {
    bottom: 20px;
    left: 20px;
}

.toast {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    border-left: 4px solid var(--info-color);
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    opacity: 0;
    pointer-events: auto;
    overflow: hidden;
    position: relative;
}

.toast.toast-show {
    transform: translateX(0);
    opacity: 1;
}

.toast.toast-hide {
    transform: translateX(100%);
    opacity: 0;
    margin-bottom: -100px;
}

.toast.toast-success {
    border-left-color: var(--success-color);
}

.toast.toast-error {
    border-left-color: var(--danger-color);
}

.toast.toast-warning {
    border-left-color: var(--warning-color);
}

.toast.toast-loading {
    border-left-color: var(--samba-red);
}

.toast-content {
    position: relative;
    z-index: 2;
}

.toast-header {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    gap: 12px;
}

.toast-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-icon i {
    font-size: 18px;
}

.toast.toast-success .toast-icon i {
    color: var(--success-color);
}

.toast.toast-error .toast-icon i {
    color: var(--danger-color);
}

.toast.toast-warning .toast-icon i {
    color: var(--warning-color);
}

.toast.toast-loading .toast-icon i {
    color: var(--samba-red);
}

.toast-text {
    flex: 1;
    min-width: 0;
}

.toast-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 4px;
    line-height: 1.3;
}

.toast-message {
    font-size: 14px;
    color: var(--text-muted);
    line-height: 1.4;
    word-wrap: break-word;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    transition: all 0.2s ease;
    flex-shrink: 0;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-close:hover {
    background: var(--background-light);
    color: var(--text-dark);
    transform: scale(1.1);
}

.toast-actions {
    padding: 0 16px 16px;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.toast-action-btn {
    background: var(--samba-red);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.toast-action-btn:hover {
    background: var(--samba-dark-red);
    transform: translateY(-1px);
}

.toast-action-btn:active {
    transform: translateY(0);
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.toast-progress-bar {
    height: 100%;
    background: var(--samba-red);
    width: 100%;
    transform-origin: left;
    animation: toastProgress linear forwards;
}

@keyframes toastProgress {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

.toast.toast-success .toast-progress-bar {
    background: var(--success-color);
}

.toast.toast-error .toast-progress-bar {
    background: var(--danger-color);
}

.toast.toast-warning .toast-progress-bar {
    background: var(--warning-color);
}

/* Toast hover effects */
.toast:hover {
    transform: translateX(-5px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.toast:hover .toast-progress-bar {
    animation-play-state: paused;
}

/* Toast stacking animation */
.toast:not(:last-child) {
    margin-bottom: 8px;
}

.toast-container .toast:nth-child(n+4) {
    opacity: 0.8;
    transform: translateX(0) scale(0.95);
}

.toast-container .toast:nth-child(n+6) {
    display: none;
}

/* Chart Data Modal */
.chart-data-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.chart-data-modal .modal-content {
    background: var(--background-white);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.chart-data-modal .modal-header {
    background: linear-gradient(135deg, var(--samba-dark-red), var(--samba-red));
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-data-modal .modal-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chart-data-modal .modal-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.chart-data-modal .modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.chart-data-modal .modal-body {
    padding: 20px;
}

.chart-data-modal .modal-body p {
    margin: 8px 0;
    font-size: 14px;
    color: var(--text-dark);
    line-height: 1.4;
}

.chart-data-modal .modal-body p:first-child {
    margin-top: 0;
}

.chart-data-modal .modal-body p:last-child {
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .summary-metrics {
        grid-template-columns: repeat(3, 1fr);
    }

    .metrics-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .chart-container {
        min-height: 300px;
    }

    .chart-container > div[id$="-chart"] {
        height: 250px !important;
        min-height: 250px;
    }

    .toast-container {
        right: 10px;
        max-width: 350px;
    }
}

@media (max-width: 768px) {
    .summary-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .filter-row {
        flex-direction: column;
        gap: 10px;
    }

    .filter-group {
        min-width: auto;
    }

    .chart-container {
        min-height: 280px;
        padding: 10px;
    }

    .chart-container > div[id$="-chart"] {
        height: 220px !important;
        min-height: 220px;
    }

    .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 12px;
    }

    .toast-container {
        right: 5px;
        left: 5px;
        max-width: none;
    }

    .toast-content {
        padding: 12px;
    }

    .toast-message {
        font-size: 13px;
    }

    /* Enhanced mobile interactions */
    .btn {
        min-height: 44px; /* Touch-friendly button size */
        padding: 10px 16px;
    }

    .btn-sm {
        min-height: 38px;
        padding: 8px 12px;
    }

    /* Mobile-friendly table */
    .table-responsive {
        max-height: 400px;
    }

    .table td {
        padding: 8px 6px;
        font-size: 13px;
    }

    /* Mobile multi-select */
    .multi-select-dropdown {
        max-height: 250px;
    }

    .multi-select-option {
        padding: 12px 10px;
        font-size: 14px;
    }

    /* Mobile comparison section */
    .comparison-kpis {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .comparison-metric-card {
        padding: 12px;
    }

    .comparison-metric-value {
        font-size: 20px;
    }

    /* Mobile export buttons */
    .export-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .export-buttons .btn {
        width: 100%;
    }

    /* Mobile loading indicator */
    .loading-indicator {
        padding: 40px 20px;
    }

    .advanced-spinner {
        width: 50px;
        height: 50px;
    }

    .advanced-spinner::before {
        width: 50px;
        height: 50px;
    }

    .advanced-spinner::after {
        width: 30px;
        height: 30px;
        top: 10px;
        left: 10px;
    }
}

@media (max-width: 480px) {
    .summary-metrics {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .quick-dates {
        justify-content: center;
        flex-wrap: wrap;
        gap: 5px;
    }

    .quick-dates .btn {
        flex: 1;
        min-width: 80px;
        font-size: 11px;
        padding: 6px 8px;
    }

    .chart-container {
        min-height: 250px;
        padding: 8px;
    }

    .chart-container > div[id$="-chart"] {
        height: 200px !important;
        min-height: 200px;
    }

    .chart-title {
        font-size: 14px;
    }

    .toast-container {
        top: 10px;
        right: 5px;
        left: 5px;
    }

    .toast-content {
        padding: 10px;
        font-size: 12px;
    }

    .toast-content i {
        font-size: 16px;
    }

    /* Ultra-mobile optimizations */
    .summary-metric {
        padding: 12px;
    }

    .metric-value {
        font-size: 18px;
    }

    .metric-label {
        font-size: 11px;
    }

    .filter-section {
        padding: 15px;
    }

    .content-card {
        padding: 15px;
        margin-bottom: 15px;
    }

    .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .nav-tabs .nav-link {
        white-space: nowrap;
        padding: 6px 10px;
        font-size: 11px;
    }

    /* Mobile table enhancements */
    .table-responsive {
        max-height: 300px;
        font-size: 12px;
    }

    .table th {
        font-size: 11px;
        padding: 6px 4px;
    }

    .table td {
        padding: 6px 4px;
        font-size: 11px;
    }

    /* Mobile modal adjustments */
    .chart-data-modal .modal-content,
    .row-details-modal .modal-content {
        width: 95%;
        margin: 10px;
    }

    .row-details-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    /* Mobile loading states */
    .loading-indicator {
        padding: 30px 15px;
    }

    .loading-text {
        font-size: 16px;
    }

    .loading-subtext {
        font-size: 12px;
    }

    /* Mobile export progress */
    .export-progress-content {
        padding: 30px 20px;
        min-width: 250px;
    }

    /* Mobile skeleton loading */
    .skeleton-chart {
        height: 200px;
    }

    .skeleton-card {
        height: 80px;
    }
}

/* Mobile Touch Interactions */
.touch-active {
    background-color: rgba(196, 0, 0, 0.1) !important;
    transform: scale(0.98);
    transition: all 0.1s ease;
}

/* Pull-to-refresh hint */
.pull-refresh-hint {
    position: fixed;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--samba-dark-red);
    color: white;
    padding: 10px 20px;
    border-radius: 0 0 20px 20px;
    z-index: 9999;
    transition: top 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.pull-refresh-hint.active {
    top: 0;
}

.pull-refresh-hint .hint-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.pull-refresh-hint .hint-content i {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* Enhanced touch targets for mobile */
@media (max-width: 768px) {
    .btn, .nav-link, .multi-select-trigger, .summary-metric {
        position: relative;
        overflow: hidden;
    }

    .btn::after, .nav-link::after, .multi-select-trigger::after, .summary-metric::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
    }

    .btn:active::after, .nav-link:active::after,
    .multi-select-trigger:active::after, .summary-metric:active::after {
        width: 200px;
        height: 200px;
    }

    /* Improved scrolling for mobile */
    .table-responsive, .multi-select-dropdown, .charts-container {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    /* Mobile-optimized focus states */
    .form-control:focus, .btn:focus, .nav-link:focus {
        outline: 2px solid var(--samba-red);
        outline-offset: 2px;
    }

    /* Prevent zoom on input focus */
    input, select, textarea {
        font-size: 16px !important;
    }
}

/* Hide Plotly default placeholder messages */
.plotly .modebar-group .modebar-btn[data-title*="Interactive"] {
    display: none !important;
}

.plotly .js-plotly-plot .plotly-notifier {
    display: none !important;
}

.plotly .main-svg .infolayer .g-gtitle .gtitle {
    display: block !important;
}

/* Hide any default Plotly messages */
.chart-container .plotly-graph-div .js-plotly-plot .plotly .main-svg .infolayer .g-gtitle text[data-unformatted*="Interactive"] {
    display: none !important;
}

.chart-container .plotly-graph-div .js-plotly-plot .plotly .main-svg .infolayer .g-gtitle text[data-unformatted*="Click and drag"] {
    display: none !important;
}

/* Ensure charts are always visible */
.chart-container > div[id$="-chart"]:not(.chart-placeholder) {
    min-height: 400px !important;
    background: transparent !important;
    border: none !important;
}

/* Remove placeholder styling when chart is rendered */
.chart-container > div[id$="-chart"]:not(.chart-placeholder) .chart-placeholder-content {
    display: none !important;
}
