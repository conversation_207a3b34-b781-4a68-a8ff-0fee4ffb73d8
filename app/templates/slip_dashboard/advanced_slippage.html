{% extends "base.html" %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/slip_home.css') }}"/>
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced_slippage.css') }}"/>
<link href='https://fonts.googleapis.com/css?family=Expletus Sans' rel='stylesheet'/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.1/dist/css/bootstrap.min.css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"/>
<script src='https://cdn.plot.ly/plotly-latest.min.js'></script>
<style>
/* Enhanced SAMBA Theme Colors */
:root {
    --primary-color: #4d0000;
    --secondary-color: #c50000;
    --accent-blue: #0066cc;
    --accent-green: #28a745;
    --accent-orange: #fd7e14;
    --positive-color: #28a745;
    --negative-color: #dc3545;
    --neutral-color: #6c757d;
    --background-light: #f8f9fa;
    --background-white: #ffffff;
    --text-dark: #2f4251;
    --text-muted: #6c757d;
    --border-light: #e9ecef;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 6px rgba(0,0,0,0.1);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-light: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

body {
    font-family: 'Expletus Sans', sans-serif;
    background-color: var(--background-light);
    line-height: 1.5;
}

.page-header {
    background: var(--gradient-primary);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-medium);
    position: relative;
}

.page-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}

.page-header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

.refresh-info {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 12px;
    opacity: 0.8;
}

/* Dashboard Summary */
.dashboard-summary {
    background: var(--background-white);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
}

.summary-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.summary-metric {
    background: var(--gradient-light);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid var(--primary-color);
}

.summary-metric.positive { border-left-color: var(--positive-color); }
.summary-metric.negative { border-left-color: var(--negative-color); }
.summary-metric.neutral { border-left-color: var(--neutral-color); }

.metric-value {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 5px;
}

.metric-value.positive { color: var(--positive-color); }
.metric-value.negative { color: var(--negative-color); }
.metric-value.neutral { color: var(--primary-color); }

.metric-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    font-weight: 500;
}

.metric-change {
    font-size: 10px;
    margin-top: 3px;
}

/* Tab Navigation */
.nav-tabs {
    border-bottom: 2px solid var(--primary-color);
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    color: var(--text-dark);
    border: none;
    padding: 10px 20px;
    font-weight: 600;
    border-radius: 8px 8px 0 0;
    margin-right: 5px;
    font-size: 14px;
}

.nav-tabs .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    border-bottom: 2px solid var(--primary-color);
}

.nav-tabs .nav-link:hover {
    background-color: rgba(77, 0, 0, 0.1);
}

/* Filter Panel */
.filter-panel {
    background: var(--background-white);
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: var(--shadow-light);
    margin-bottom: 20px;
    border: 1px solid var(--border-light);
}

.filter-panel h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 16px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.filter-group {
    flex: 1;
    min-width: 150px;
}

.filter-group.date-range { min-width: 200px; }
.filter-group.slave-group { min-width: 180px; max-width: 200px; }
.filter-group.actions { flex: 0 0 auto; min-width: 160px; }

.form-control, .form-select {
    border: 1px solid var(--border-light);
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 13px;
    height: 36px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(77, 0, 0, 0.15);
}

.form-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 5px;
}

/* Multi-select Dropdown */
.multi-select-wrapper {
    position: relative;
    width: 100%;
}

.multi-select-trigger {
    width: 100%;
    padding: 6px 30px 6px 10px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    background: white;
    cursor: pointer;
    font-size: 13px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.multi-select-trigger:hover { border-color: var(--primary-color); }
.multi-select-trigger.open {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(77, 0, 0, 0.15);
}

.multi-select-display {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.multi-select-count {
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    margin-left: 5px;
}

.multi-select-arrow {
    position: absolute;
    right: 8px;
    transition: transform 0.3s;
    color: var(--text-muted);
}

.multi-select-arrow.open { transform: rotate(180deg); }

.multi-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 250px;
    overflow: hidden;
    display: none;
    margin-top: 2px;
}

.multi-select-dropdown.show { display: block; }

.multi-select-header {
    padding: 8px;
    border-bottom: 1px solid var(--border-light);
    background: var(--background-light);
}

.multi-select-search {
    width: 100%;
    border: 1px solid var(--border-light);
    border-radius: 4px;
    padding: 5px 8px;
    font-size: 12px;
    margin-bottom: 8px;
}

.multi-select-actions {
    display: flex;
    gap: 5px;
}

.multi-select-action-btn {
    flex: 1;
    padding: 4px 8px;
    font-size: 11px;
    border: 1px solid var(--border-light);
    background: white;
    border-radius: 3px;
    cursor: pointer;
    color: var(--text-muted);
}

.multi-select-action-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.multi-select-options {
    max-height: 150px;
    overflow-y: auto;
}

.multi-select-option {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.multi-select-option:hover { background-color: var(--background-light); }
.multi-select-option.selected { background-color: rgba(77, 0, 0, 0.1); }

.multi-select-checkbox {
    margin-right: 8px;
    width: 14px;
    height: 14px;
    accent-color: var(--primary-color);
}

.multi-select-label { flex: 1; }

.multi-select-option.selected .multi-select-label {
    color: var(--primary-color);
    font-weight: 500;
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 8px 16px;
    font-weight: 600;
    border-radius: 6px;
    font-size: 13px;
    height: 36px;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--neutral-color);
    border-color: var(--neutral-color);
    padding: 6px 12px;
    font-weight: 500;
    border-radius: 6px;
    font-size: 13px;
    height: 36px;
}

/* Quick Date Buttons */
.quick-dates {
    display: flex;
    gap: 8px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.quick-date-btn {
    padding: 4px 8px;
    font-size: 11px;
    border: 1px solid var(--border-light);
    background: white;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-muted);
}

.quick-date-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Content Cards */
.content-card {
    background: var(--background-white);
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-light);
}

.content-card h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 18px;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
}

/* Chart Containers */
.chart-container {
    background: var(--background-white);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
    min-height: 400px;
    border: 1px solid var(--border-light);
    position: relative;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chart-container.compact { min-height: 320px; }
.chart-container.large { min-height: 500px; }

.chart-container > div[id$="-chart"] {
    width: 100% !important;
    flex: 1;
    min-height: 350px;
    max-width: 100%;
    overflow: visible;
    display: block;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
    flex-shrink: 0;
    padding: 5px 0;
}

.chart-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    flex: 1;
}

/* Loading States */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--text-muted);
    flex-direction: column;
    gap: 10px;
}

.spinner {
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    padding: 3px 6px;
    border-radius: 12px;
    font-weight: 500;
}

.status-indicator.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--positive-color);
}

.status-indicator.info {
    background-color: rgba(0, 102, 204, 0.1);
    color: var(--accent-blue);
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: currentColor;
}

/* Responsive Design */
@media (max-width: 768px) {
    .summary-metrics {
        grid-template-columns: repeat(2, 1fr);
    }

    .filter-row {
        flex-direction: column;
        gap: 10px;
    }

    .filter-group {
        min-width: auto;
    }

    .chart-container {
        min-height: 320px;
        padding: 8px;
    }
}

@media (max-width: 480px) {
    .summary-metrics {
        grid-template-columns: 1fr;
    }

    .chart-container {
        min-height: 250px;
    }
}
</style>
{% endblock head %}

{% block content %}
<main>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fa fa-line-chart"></i> Advanced Slippage Analysis</h1>
            <p>Comprehensive slippage monitoring and trade comparison dashboard</p>
            <div class="refresh-info">
                <div class="status-indicator info">
                    <span class="status-dot"></span>
                    Last updated: <span id="last-update-time">Loading...</span>
                </div>
            </div>
        </div>

        <!-- Dashboard Summary -->
        <div class="dashboard-summary">
            <div class="summary-metrics" id="summary-metrics">
                <div class="summary-metric positive">
                    <div class="metric-value positive" id="total-slippage">Loading...</div>
                    <div class="metric-label">Total Slippage</div>
                    <div class="metric-change" id="total-slippage-change">--</div>
                </div>
                <div class="summary-metric negative">
                    <div class="metric-value negative" id="execution-slippage">Loading...</div>
                    <div class="metric-label">Execution Slippage</div>
                    <div class="metric-change" id="execution-slippage-change">--</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="avg-execution-time">Loading...</div>
                    <div class="metric-label">Avg Execution Time</div>
                    <div class="metric-change" id="avg-execution-time-change">--</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="total-turnover">Loading...</div>
                    <div class="metric-label">Total Turnover</div>
                    <div class="metric-change" id="total-turnover-change">--</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="total-trades">Loading...</div>
                    <div class="metric-label">Total Trades</div>
                    <div class="metric-change" id="total-trades-change">--</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral" id="slip-to-turnover-ratio">Loading...</div>
                    <div class="metric-label">Slip-to-Turnover Ratio</div>
                    <div class="metric-change" id="slip-to-turnover-change">--</div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="slippage-tab" data-target="slippage-panel" type="button" role="tab">
                    <i class="fa fa-bar-chart"></i> Slippage Monitoring
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="comparison-tab" data-target="comparison-panel" type="button" role="tab">
                    <i class="fa fa-exchange"></i> Trade Vision
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analytics-tab" data-target="analytics-panel" type="button" role="tab">
                    <i class="fa fa-dashboard"></i> Combined Analytics
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Slippage Monitoring Panel -->
            <div class="tab-pane show active" id="slippage-panel" role="tabpanel">
                <!-- Filter Panel -->
                <div class="filter-panel">
                    <h5><i class="fa fa-filter"></i> Analysis Filters</h5>

                    <!-- First Row: Date Range, Segment, Strategy/Cluster -->
                    <div class="filter-row">
                        <div class="filter-group date-range">
                            <label class="form-label">Date Range</label>
                            <div class="d-flex gap-2">
                                <input type="date" class="form-control" id="start-date" value="2024-01-01">
                                <input type="date" class="form-control" id="end-date" value="2024-01-31">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Segment</label>
                            <select class="form-select" id="segment-select">
                                <option value="">All Segments</option>
                                <option value="OPTIDX" selected>OPTIDX</option>
                                <option value="OPTIDX_BSE">OPTIDX_BSE</option>
                                <option value="OPTIDX_US">OPTIDX_US</option>
                                <option value="OPTSTK">OPTSTK</option>
                                <option value="FUTSTK">FUTSTK</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Exchange</label>
                            <select class="form-select" id="exchange-select">
                                <option value="">All Exchanges</option>
                                <option value="IND" selected>IND (India Combined)</option>
                                <option value="NSE">NSE</option>
                                <option value="BSE">BSE</option>
                                <option value="US">US</option>
                            </select>
                        </div>
                    </div>

                    <!-- Second Row: Quick Dates, Strategy/Slave Selection, Actions -->
                    <div class="filter-row">
                        <div class="filter-group">
                            <label class="form-label">Quick Dates</label>
                            <div class="quick-dates">
                                <button class="quick-date-btn" data-days="7">7d</button>
                                <button class="quick-date-btn" data-days="30">30d</button>
                                <button class="quick-date-btn" data-days="90">90d</button>
                                <button class="quick-date-btn" data-days="365">1y</button>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Strategy/Slave</label>
                            <select class="form-select" id="strategy-slave-select">
                                <option value="">All Strategies/Slaves</option>
                            </select>
                        </div>

                        <div class="filter-group actions">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-secondary" id="load-filters-btn">
                                    <i class="fa fa-refresh"></i> Load Options
                                </button>
                                <button type="button" class="btn btn-primary" id="run-analysis-btn">
                                    <i class="fa fa-play"></i> Run Analysis
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="content-card">
                    <h4><i class="fa fa-line-chart"></i> Slippage Analysis Charts</h4>

                    <!-- Main Slippage Trend Chart -->
                    <div class="chart-container large">
                        <div class="chart-header">
                            <h5 class="chart-title">Slippage Trend Over Time</h5>
                        </div>
                        <div id="slippage-trend-chart">
                            <div class="loading">
                                <div class="spinner"></div>
                                <span>Loading chart data...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Strategy Comparison Chart -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h5 class="chart-title">Strategy/Slave Comparison</h5>
                        </div>
                        <div id="strategy-comparison-chart">
                            <div class="loading">
                                <div class="spinner"></div>
                                <span>Loading chart data...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trade Vision Panel -->
            <div class="tab-pane fade" id="comparison-panel" role="tabpanel">
                <div class="content-card">
                    <h4><i class="fa fa-exchange"></i> Trade Vision - Strategy Comparison</h4>
                    <p class="text-muted">Compare strategy performance against cluster averages and backtest results</p>

                    <!-- Trade Vision Filter Panel -->
                    <div class="filter-panel">
                        <h5><i class="fa fa-filter"></i> Comparison Filters</h5>

                        <div class="filter-row">
                            <div class="filter-group date-range">
                                <label class="form-label">Date Range</label>
                                <div class="d-flex gap-2">
                                    <input type="date" class="form-control" id="tv-start-date" value="2024-01-01">
                                    <input type="date" class="form-control" id="tv-end-date" value="2024-01-31">
                                </div>
                            </div>

                            <div class="filter-group">
                                <label class="form-label">Strategy/Slave</label>
                                <select class="form-select" id="tv-strategy-select">
                                    <option value="">Select strategy/slave...</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="form-label">Segment</label>
                                <select class="form-select" id="tv-segment-select">
                                    <option value="OPTIDX" selected>OPTIDX</option>
                                    <option value="OPTIDX_BSE">OPTIDX_BSE</option>
                                    <option value="OPTIDX_US">OPTIDX_US</option>
                                    <option value="OPTSTK">OPTSTK</option>
                                    <option value="FUTSTK">FUTSTK</option>
                                </select>
                            </div>

                            <div class="filter-group actions">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-primary" id="tv-run-comparison-btn">
                                    <i class="fa fa-play"></i> Run Comparison
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Trade Vision Results -->
                    <div id="tv-results" style="display: none;">
                        <div class="chart-container">
                            <div class="chart-header">
                                <h5 class="chart-title">Performance Comparison</h5>
                            </div>
                            <div id="tv-comparison-chart">
                                <div class="loading">
                                    <div class="spinner"></div>
                                    <span>Loading comparison data...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Combined Analytics Panel -->
            <div class="tab-pane fade" id="analytics-panel" role="tabpanel">
                <div class="content-card">
                    <h4><i class="fa fa-dashboard"></i> Combined Analytics</h4>
                    <p class="text-muted">Integrated view combining slippage analysis with trade comparison metrics</p>

                    <!-- Coming Soon Placeholder -->
                    <div class="text-center py-5">
                        <i class="fa fa-cogs fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Advanced Analytics Coming Soon</h5>
                        <p class="text-muted">This section will provide integrated analysis combining both slippage monitoring and trade comparison features.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// Advanced Slippage Analysis JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Advanced Slippage Analysis initialized');

    // Initialize components
    initializeFilters();
    initializeTabs();
    updateTimestamp();

    // Load initial data
    loadFilterOptions();
});

// Tab functionality
function initializeTabs() {
    document.querySelectorAll('#mainTabs .nav-link').forEach(tab => {
        tab.addEventListener('click', function() {
            const targetId = this.dataset.target;

            // Update tab states
            document.querySelectorAll('#mainTabs .nav-link').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(p => {
                p.classList.remove('show', 'active');
            });

            this.classList.add('active');
            document.getElementById(targetId).classList.add('show', 'active');
        });
    });
}

// Filter functionality
function initializeFilters() {
    // Quick date buttons
    document.querySelectorAll('.quick-date-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const days = parseInt(this.dataset.days);
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - days);

            document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
            document.getElementById('end-date').value = endDate.toISOString().split('T')[0];
        });
    });

    // Load filters button
    document.getElementById('load-filters-btn')?.addEventListener('click', loadFilterOptions);

    // Run analysis button
    document.getElementById('run-analysis-btn')?.addEventListener('click', runSlippageAnalysis);

    // Trade Vision run comparison button
    document.getElementById('tv-run-comparison-btn')?.addEventListener('click', runTradeVisionComparison);
}

// Load filter options from backend
async function loadFilterOptions() {
    try {
        showLoading('Loading filter options...');

        const response = await fetch('/slip_dashboard/api/filter_options');
        const data = await response.json();

        if (data.success) {
            populateFilterOptions(data.data);
            showNotification('Filter options loaded successfully', 'success');
        } else {
            throw new Error(data.error || 'Failed to load filter options');
        }
    } catch (error) {
        console.error('Error loading filter options:', error);
        showNotification('Failed to load filter options: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// Populate filter dropdowns
function populateFilterOptions(data) {
    // Populate strategy/slave dropdown
    const strategySelect = document.getElementById('strategy-slave-select');
    const tvStrategySelect = document.getElementById('tv-strategy-select');

    if (strategySelect && data.strategies) {
        strategySelect.innerHTML = '<option value="">All Strategies/Slaves</option>';
        data.strategies.forEach(strategy => {
            strategySelect.innerHTML += `<option value="${strategy}">${strategy}</option>`;
        });
    }

    if (tvStrategySelect && data.strategies) {
        tvStrategySelect.innerHTML = '<option value="">Select strategy/slave...</option>';
        data.strategies.forEach(strategy => {
            tvStrategySelect.innerHTML += `<option value="${strategy}">${strategy}</option>`;
        });
    }
}

// Run slippage analysis
async function runSlippageAnalysis() {
    try {
        showLoading('Running slippage analysis...');

        const filters = {
            start_date: document.getElementById('start-date').value,
            end_date: document.getElementById('end-date').value,
            segments: [document.getElementById('segment-select').value].filter(Boolean),
            exchanges: [document.getElementById('exchange-select').value].filter(Boolean),
            strategies: [document.getElementById('strategy-slave-select').value].filter(Boolean)
        };

        const response = await fetch('/slip_dashboard/api/slippage_data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filters)
        });

        const data = await response.json();

        if (data.success) {
            updateSummaryMetrics(data.summary_stats);
            createSlippageCharts(data.charts);
            showNotification('Analysis completed successfully', 'success');
        } else {
            throw new Error(data.error || 'Analysis failed');
        }
    } catch (error) {
        console.error('Error running analysis:', error);
        showNotification('Analysis failed: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// Run Trade Vision comparison
async function runTradeVisionComparison() {
    try {
        const strategySelect = document.getElementById('tv-strategy-select');
        if (!strategySelect.value) {
            showNotification('Please select a strategy/slave for comparison', 'warning');
            return;
        }

        showLoading('Running trade comparison...');

        const filters = {
            start_date: document.getElementById('tv-start-date').value,
            end_date: document.getElementById('tv-end-date').value,
            slave_strategy: strategySelect.value,
            segment: document.getElementById('tv-segment-select').value,
            exchange: 'IND'
        };

        const response = await fetch('/slip_dashboard/api/trade_comparison', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filters)
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('tv-results').style.display = 'block';
            createTradeVisionChart(data.data);
            showNotification('Comparison completed successfully', 'success');
        } else {
            throw new Error(data.error || 'Comparison failed');
        }
    } catch (error) {
        console.error('Error running comparison:', error);
        showNotification('Comparison failed: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// Update summary metrics
function updateSummaryMetrics(stats) {
    if (!stats) return;

    document.getElementById('total-slippage').textContent = formatCurrency(stats.total_slippage || 0);
    document.getElementById('execution-slippage').textContent = formatCurrency(stats.execution_slippage || 0);
    document.getElementById('avg-execution-time').textContent = formatTime(stats.avg_execution_time || 0);
    document.getElementById('total-turnover').textContent = formatCurrency(stats.total_turnover || 0);
    document.getElementById('total-trades').textContent = formatNumber(stats.total_trades || 0);
    document.getElementById('slip-to-turnover-ratio').textContent = formatBPS(stats.slip_to_turnover_bps || 0);
}

// Create slippage charts
function createSlippageCharts(charts) {
    if (charts.slippage_trend) {
        createChart('slippage-trend-chart', charts.slippage_trend);
    }
    if (charts.strategy_comparison) {
        createChart('strategy-comparison-chart', charts.strategy_comparison);
    }
}

// Create Trade Vision chart
function createTradeVisionChart(data) {
    if (data.charts && data.charts.performance_comparison) {
        createChart('tv-comparison-chart', data.charts.performance_comparison);
    }
}

// Generic chart creation function
function createChart(containerId, chartData) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // Remove loading indicator
    container.innerHTML = '';

    // Create Plotly chart
    Plotly.newPlot(containerId, chartData.data, chartData.layout, {
        responsive: true,
        displayModeBar: true,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
        displaylogo: false
    });
}

// Utility functions
function formatCurrency(value) {
    if (Math.abs(value) >= 1e7) {
        return '₹' + (value / 1e7).toFixed(1) + 'Cr';
    } else if (Math.abs(value) >= 1e5) {
        return '₹' + (value / 1e5).toFixed(1) + 'L';
    } else if (Math.abs(value) >= 1e3) {
        return '₹' + (value / 1e3).toFixed(1) + 'K';
    } else {
        return '₹' + value.toFixed(0);
    }
}

function formatTime(seconds) {
    if (seconds >= 60) {
        return (seconds / 60).toFixed(1) + 'm';
    } else {
        return seconds.toFixed(2) + 's';
    }
}

function formatNumber(value) {
    return value.toLocaleString();
}

function formatBPS(value) {
    return value.toFixed(1) + ' BPS';
}

function updateTimestamp() {
    const now = new Date();
    const timestamp = now.toLocaleString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    document.getElementById('last-update-time').textContent = timestamp;
}

// Loading and notification functions
function showLoading(message = 'Loading...') {
    // You can implement a global loading indicator here
    console.log('Loading:', message);
}

function hideLoading() {
    console.log('Loading complete');
}

function showNotification(message, type = 'info') {
    // You can implement a notification system here
    console.log(`${type.toUpperCase()}: ${message}`);

    // Simple alert for now
    if (type === 'error') {
        alert('Error: ' + message);
    }
}
</script>

                        <div class="filter-group">
                            <label class="form-label">Exchange</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="exchange-dropdown">
                                    <span class="multi-select-display">Select exchanges...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="exchange-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search exchanges...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="IND">
                                            <input type="checkbox" class="multi-select-checkbox" checked>
                                            <span class="multi-select-label">IND (India Combined)</span>
                                        </div>
                                        <div class="multi-select-option" data-value="NSE">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">NSE (National Stock Exchange)</span>
                                        </div>
                                        <div class="multi-select-option" data-value="BSE">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">BSE (Bombay Stock Exchange)</span>
                                        </div>
                                        <div class="multi-select-option" data-value="US">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">US (United States)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Second Row: Quick Dates, Slave Names, Actions -->
                    <div class="filter-row">
                        <div class="filter-group quick-dates-group">
                            <label class="form-label">Quick Dates</label>
                            <div class="quick-dates">
                                <button class="quick-date-btn" data-days="7">7d</button>
                                <button class="quick-date-btn" data-days="30">30d</button>
                                <button class="quick-date-btn" data-days="90">3m</button>
                                <button class="quick-date-btn" data-days="180">6m</button>
                                <button class="quick-date-btn" data-days="365">1y</button>
                            </div>
                        </div>

                        <div class="filter-group slave-group">
                            <label class="form-label">Slave Names</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="slave-dropdown">
                                    <span class="multi-select-display">Select slaves...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="slave-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search slaves...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="slave_001">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_001</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_002">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_002</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_003">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_003</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_hedge_001">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_hedge_001</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_hedge_002">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_hedge_002</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group actions">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" id="load-slaves-btn">
                                    <i class="fa fa-refresh"></i> Load Slaves
                                </button>
                                <button class="btn btn-primary" id="run-analysis-btn">
                                    <i class="fa fa-play"></i> Run Analysis
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Loading Indicator -->
                <div class="loading-indicator" id="loading-indicator" style="display: none;">
                    <div class="advanced-spinner"></div>
                    <div class="loading-text">Loading analysis data...</div>
                    <div class="loading-subtext">Please wait while we process your request</div>
                </div>

                <!-- Charts Container -->
                <div class="charts-container" id="charts-container">
                    <!-- Overview Chart -->
                    <div class="content-card">
                        <h4>Slippage Trend Overview</h4>
                        <div class="chart-container">
                            <div id="overview-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-line-chart fa-3x"></i>
                                    <p>Click "Run Analysis" to load slippage trend chart</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Strategy Comparison Chart -->
                    <div class="content-card">
                        <h4>Strategy Performance Comparison</h4>
                        <div class="chart-container">
                            <div id="strategy-comparison-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-bar-chart fa-3x"></i>
                                    <p>Strategy comparison will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timing Analysis Chart -->
                    <div class="content-card">
                        <h4>Execution Timing Distribution</h4>
                        <div class="chart-container">
                            <div id="timing-histogram-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-clock-o fa-3x"></i>
                                    <p>Timing analysis will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Daily Slippage Breakdown -->
                    <div class="content-card">
                        <h4>Daily Slippage Breakdown</h4>
                        <div class="chart-container">
                            <div id="daily-slippage-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-bar-chart fa-3x"></i>
                                    <p>Daily slippage breakdown will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Intraday Distribution -->
                    <div class="content-card">
                        <h4>Intraday Slippage Pattern</h4>
                        <div class="chart-container">
                            <div id="minute-distribution-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-line-chart fa-3x"></i>
                                    <p>Intraday pattern will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Execution Timing by Hour -->
                    <div class="content-card">
                        <h4>Execution Timing Analysis</h4>
                        <div class="chart-container">
                            <div id="execution-timing-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-clock-o fa-3x"></i>
                                    <p>Execution timing analysis will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Slippage by DTE -->
                    <div class="content-card">
                        <h4>Slippage by Days to Expiry</h4>
                        <div class="chart-container">
                            <div id="slippage-dte-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-calendar fa-3x"></i>
                                    <p>DTE analysis will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hedge vs Normal Analysis -->
                    <div class="content-card">
                        <h4>Hedge vs Normal Trades Analysis</h4>
                        <div class="chart-container">
                            <div id="hedge-normal-chart" class="chart-placeholder">
                                <div class="chart-placeholder-content">
                                    <i class="fa fa-exchange fa-3x"></i>
                                    <p>Hedge vs normal analysis will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Strategy Summary -->
                <div class="data-tables-container" id="data-tables-container" style="display: none;">
                    <div class="content-card">
                        <div class="table-header">
                            <h4>Strategy Summary & Analysis</h4>
                            <div class="table-controls">
                                <div class="table-control-group">
                                    <label class="form-label">Group By</label>
                                    <select class="form-select form-select-sm" id="group-by-select">
                                        <option value="strategy_name">Strategy</option>
                                        <option value="slave_name">Slave</option>
                                        <option value="date">Date</option>
                                        <option value="segment">Segment</option>
                                    </select>
                                </div>
                                <div class="table-control-group">
                                    <label class="form-label">Aggregation</label>
                                    <select class="form-select form-select-sm" id="aggregation-select">
                                        <option value="sum">Sum</option>
                                        <option value="avg">Average</option>
                                        <option value="count">Count</option>
                                        <option value="max">Maximum</option>
                                        <option value="min">Minimum</option>
                                    </select>
                                </div>
                                <div class="table-control-group">
                                    <label class="form-label">Filter Column</label>
                                    <select class="form-select form-select-sm" id="column-filter-select">
                                        <option value="">All Columns</option>
                                        <option value="strategy_name">Strategy</option>
                                        <option value="slave_name">Slave</option>
                                        <option value="total_slippage">Total Slippage</option>
                                        <option value="turnover">Turnover</option>
                                    </select>
                                </div>
                                <div class="table-control-group">
                                    <label class="form-label">Filter Value</label>
                                    <input type="text" class="form-control form-control-sm" id="column-filter-input" placeholder="Enter filter value">
                                </div>
                                <div class="table-control-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="btn-group">
                                        <button class="btn btn-sm btn-outline-secondary" id="apply-grouping-btn">Apply Grouping</button>
                                        <button class="btn btn-sm btn-success" id="export-csv-btn">
                                            <i class="fa fa-download"></i> Export CSV
                                        </button>
                                        <button class="btn btn-sm btn-info" id="export-excel-btn">
                                            <i class="fa fa-file-excel-o"></i> Export Excel
                                        </button>
                                        <button class="btn btn-sm btn-warning" id="export-json-btn">
                                            <i class="fa fa-code"></i> Export JSON
                                        </button>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fa fa-download"></i> Bulk Export
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" id="export-all-formats-btn">
                                                    <i class="fa fa-files-o"></i> All Formats (CSV + Excel + JSON)
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" id="export-summary-only-btn">
                                                    <i class="fa fa-table"></i> Summary Only (CSV)
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" id="export-charts-btn">
                                                    <i class="fa fa-bar-chart"></i> Charts as Images
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary Statistics -->
                        <div class="summary-stats-row" id="summary-stats-row">
                            <div class="summary-stat-item">
                                <span class="stat-label">Total Records:</span>
                                <span class="stat-value" id="total-records">0</span>
                            </div>
                            <div class="summary-stat-item">
                                <span class="stat-label">Filtered Records:</span>
                                <span class="stat-value" id="filtered-records">0</span>
                            </div>
                            <div class="summary-stat-item">
                                <span class="stat-label">Sum Total Slippage:</span>
                                <span class="stat-value" id="sum-total-slippage">₹0</span>
                            </div>
                            <div class="summary-stat-item">
                                <span class="stat-label">Avg Slip-to-Turnover:</span>
                                <span class="stat-value" id="avg-slip-ratio">0.00 BPS</span>
                            </div>
                        </div>

                        <!-- Enhanced Data Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="analysis-data-table">
                                <thead class="table-dark">
                                    <tr id="table-header-row">
                                        <th class="sortable" data-column="strategy_name">
                                            Strategy <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="slave_name">
                                            Slave <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="date">
                                            Date <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="total_slippage">
                                            Total Slippage <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="execution_slippage">
                                            Execution Slippage <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="turnover">
                                            Turnover <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="trade_count">
                                            Trade Count <i class="fa fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-column="slip_to_turnover_bps">
                                            Slip-to-Turnover (BPS) <i class="fa fa-sort"></i>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="analysis-table-body">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="table-pagination" id="table-pagination">
                            <div class="pagination-info">
                                Showing <span id="showing-start">0</span> to <span id="showing-end">0</span> of <span id="total-rows">0</span> entries
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-sm btn-outline-secondary" id="prev-page-btn" disabled>
                                    <i class="fa fa-chevron-left"></i> Previous
                                </button>
                                <select class="form-select form-select-sm" id="page-size-select">
                                    <option value="25">25 per page</option>
                                    <option value="50" selected>50 per page</option>
                                    <option value="100">100 per page</option>
                                    <option value="all">Show All</option>
                                </select>
                                <button class="btn btn-sm btn-outline-secondary" id="next-page-btn" disabled>
                                    Next <i class="fa fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trade Vision Panel -->
            <div class="tab-pane fade" id="comparison-panel" role="tabpanel">
                <div class="filter-panel">
                    <h5><i class="fa fa-filter"></i> Comparison Filters</h5>
                    <div class="filter-row">
                        <div class="filter-group date-range">
                            <label class="form-label">Date Range</label>
                            <div class="d-flex gap-2">
                                <input type="date" class="form-control" id="comparison-start-date" value="2024-01-01">
                                <input type="date" class="form-control" id="comparison-end-date" value="2024-01-31">
                            </div>
                        </div>
                        <div class="filter-group">
                            <label class="form-label">Slave/Strategy</label>
                            <input type="text" class="form-control" id="comparison-slave-input" placeholder="Enter slave/strategy name">
                        </div>
                        <div class="filter-group">
                            <label class="form-label">Segment</label>
                            <select class="form-select" id="comparison-segment-select">
                                <option value="OPTIDX" selected>OPTIDX</option>
                                <option value="OPTIDX_BSE">OPTIDX_BSE</option>
                                <option value="OPTIDX_US">OPTIDX_US</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="form-label">Exchange</label>
                            <select class="form-select" id="comparison-exchange-select">
                                <option value="IND" selected>IND</option>
                                <option value="NSE">NSE</option>
                                <option value="BSE">BSE</option>
                                <option value="US">US</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary w-100" id="run-comparison-btn">Run Comparison</button>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Comparison Results -->
                <div class="comparison-results" id="comparison-results" style="display: none;">
                    <!-- Comparison Summary Card -->
                    <div class="content-card">
                        <h4><i class="fa fa-exchange"></i> Strategy vs Cluster vs Backtest Comparison</h4>

                        <!-- Key Performance Indicators -->
                        <div class="comparison-kpis">
                            <div class="kpi-section">
                                <h6>Strategy vs Cluster</h6>
                                <div class="metrics-grid" id="strategy-cluster-metrics">
                                    <!-- Metrics populated by JavaScript -->
                                </div>
                            </div>
                            <div class="kpi-section">
                                <h6>Strategy vs Backtest</h6>
                                <div class="metrics-grid" id="strategy-backtest-metrics">
                                    <!-- Metrics populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comparison Charts -->
                    <div class="content-card">
                        <h4><i class="fa fa-bar-chart"></i> Performance Comparison Charts</h4>

                        <!-- Chart Navigation Tabs -->
                        <ul class="nav nav-pills nav-fill mb-3" id="comparison-chart-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="exit-count-tab" data-target="exit-count-panel" type="button" role="tab">
                                    Exit Count Comparison
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pnl-comparison-tab" data-target="pnl-comparison-panel" type="button" role="tab">
                                    PnL Comparison
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="holding-time-tab" data-target="holding-time-panel" type="button" role="tab">
                                    Holding Time Distribution
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="performance-summary-tab" data-target="performance-summary-panel" type="button" role="tab">
                                    Performance Summary
                                </button>
                            </li>
                        </ul>

                        <!-- Chart Tab Content -->
                        <div class="tab-content" id="comparison-chart-content">
                            <!-- Exit Count Panel -->
                            <div class="tab-pane show active" id="exit-count-panel" role="tabpanel">
                                <div class="chart-container">
                                    <div class="chart-header">
                                        <div class="chart-title">Daily Exit Count Comparison</div>
                                        <div class="chart-controls">
                                            <button class="btn btn-sm btn-outline-secondary" id="toggle-exit-count-view">
                                                <i class="fa fa-refresh"></i> Toggle View
                                            </button>
                                        </div>
                                    </div>
                                    <div id="exit-count-chart" class="chart-placeholder">
                                        <div class="chart-placeholder-content">
                                            <i class="fa fa-line-chart fa-2x"></i>
                                            <p>Exit count comparison will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- PnL Comparison Panel -->
                            <div class="tab-pane fade" id="pnl-comparison-panel" role="tabpanel">
                                <div class="chart-container">
                                    <div class="chart-header">
                                        <div class="chart-title">Cumulative PnL Comparison</div>
                                        <div class="chart-controls">
                                            <button class="btn btn-sm btn-outline-secondary" id="toggle-pnl-view">
                                                <i class="fa fa-line-chart"></i> Line View
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" id="reset-pnl-zoom">
                                                <i class="fa fa-search-minus"></i> Reset Zoom
                                            </button>
                                        </div>
                                    </div>
                                    <div id="pnl-comparison-chart" class="chart-placeholder">
                                        <div class="chart-placeholder-content">
                                            <i class="fa fa-area-chart fa-2x"></i>
                                            <p>PnL comparison will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Holding Time Panel -->
                            <div class="tab-pane fade" id="holding-time-panel" role="tabpanel">
                                <div class="chart-container">
                                    <div class="chart-header">
                                        <div class="chart-title">Holding Time Distribution</div>
                                        <div class="chart-controls">
                                            <select class="form-select form-select-sm" id="holding-time-bins">
                                                <option value="20">20 bins</option>
                                                <option value="30" selected>30 bins</option>
                                                <option value="50">50 bins</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div id="holding-time-chart" class="chart-placeholder">
                                        <div class="chart-placeholder-content">
                                            <i class="fa fa-bar-chart fa-2x"></i>
                                            <p>Holding time distribution will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Performance Summary Panel -->
                            <div class="tab-pane fade" id="performance-summary-panel" role="tabpanel">
                                <div class="performance-summary-container">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover" id="performance-summary-table">
                                                    <thead class="table-dark">
                                                        <tr>
                                                            <th>Metric</th>
                                                            <th>Strategy</th>
                                                            <th>Cluster</th>
                                                            <th>Backtest</th>
                                                            <th>Strategy vs Cluster</th>
                                                            <th>Strategy vs Backtest</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="performance-summary-body">
                                                        <!-- Data populated by JavaScript -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="performance-insights">
                                                <h6><i class="fa fa-lightbulb-o"></i> Key Insights</h6>
                                                <div id="performance-insights-content">
                                                    <!-- Insights populated by JavaScript -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Analysis -->
                    <div class="content-card">
                        <h4><i class="fa fa-table"></i> Detailed Trade Analysis</h4>

                        <div class="analysis-controls">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Analysis Type</label>
                                    <select class="form-select" id="analysis-type-select">
                                        <option value="daily">Daily Analysis</option>
                                        <option value="weekly">Weekly Analysis</option>
                                        <option value="monthly">Monthly Analysis</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Metric Focus</label>
                                    <select class="form-select" id="metric-focus-select">
                                        <option value="exit_count">Exit Count</option>
                                        <option value="pnl">PnL</option>
                                        <option value="holding_time">Holding Time</option>
                                        <option value="all">All Metrics</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Short Duration Threshold</label>
                                    <input type="number" class="form-control" id="short-duration-threshold" value="5" min="1" max="60">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-primary w-100" id="refresh-analysis-btn">
                                        <i class="fa fa-refresh"></i> Refresh Analysis
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="detailed-analysis-results" id="detailed-analysis-results">
                            <!-- Results populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Combined Analytics Panel -->
            <div class="tab-pane fade" id="analytics-panel" role="tabpanel">
                <div class="content-card">
                    <h4>Combined Analytics Dashboard</h4>
                    <p class="text-muted">Integrated view combining slippage analysis with trade comparison metrics</p>
                    
                    <!-- Coming Soon Placeholder -->
                    <div class="text-center py-5">
                        <i class="fa fa-cogs fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Advanced Analytics Coming Soon</h5>
                        <p class="text-muted">This section will provide integrated analysis combining both slippage monitoring and trade comparison features.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script src="{{ url_for('static', filename='scripts/advanced_slippage.js') }}"></script>
{% endblock content %}
