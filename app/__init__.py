from flask import Flask
from config import DevelopmentConfig, TestingConfig
from app.models import db, login_manager, minio
from flasgger import Swagger
from swagger_config import template, swagger_config
import logging
from flask_login import current_user
from clickhouse_driver import Client
from kafka import KafkaProducer
from json import dumps


class _Formatter(logging.Formatter):
    def format(self, record):
        try:
            record.username = current_user.username
        except Exception as e:  # noqa: F841
            record.username = "guest"
        return super(_Formatter, self).format(record)


def create_app(config=DevelopmentConfig):
    formatter = _Formatter(config.LOGGING_FORMAT, datefmt="%Y-%m-%d %H:%M:%S")
    fh = logging.FileHandler(config.LOGGING_PATH, "a")
    fh.setLevel(logging.INFO)
    fh.setFormatter(formatter)
    app = Flask(__name__)
    app.config.from_object(config)
    app.logger.addHandler(fh)
    app.logger.setLevel(logging.INFO)
    db.init_app(app)
    db.app = app
    login_manager.init_app(app)
    login_manager.app = app
    app.config["CLICKHOUSE_CLIENT"] = Client(
        host=app.config["CLICKHOUSE_HOST"],
        port=app.config["CLICKHOUSE_PORT"],
        user=app.config["CLICKHOUSE_USER"],
        password=app.config["CLICKHOUSE_PASS"],
        database=app.config["CLICKHOUSE_DB"],
    )
    if config != TestingConfig:
        minio.init_app(app)
        minio.app = app
        app.config["KAFKA_PRODUCER"] = KafkaProducer(
            bootstrap_servers=app.config["KAFKA_SERVER"],
            value_serializer=lambda x: dumps(x).encode("utf-8"),
        )

    # register blueprints of applications
    from app.main import main as main_bp

    app.register_blueprint(main_bp)

    # register the authentication blueprint
    from app.auth import auth as auth_bp

    app.register_blueprint(auth_bp)

    from app.strat_add import strat_add as strat_add_bp

    app.register_blueprint(strat_add_bp)

    from app.slip_dashboard import slip_dashboard as slip_dashboard_bp

    app.register_blueprint(slip_dashboard_bp)

    Swagger(app, config=swagger_config, template=template)

    return app
