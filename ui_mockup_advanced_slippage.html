<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Advanced Slippage Analysis - SAMBA</title>
    <link href='https://fonts.googleapis.com/css?family=Expletus Sans' rel='stylesheet'/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.1/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"/>
    <style>
        /* Enhanced SAMBA Theme Colors */
        :root {
            --primary-color: #4d0000;
            --secondary-color: #c50000;
            --accent-blue: #0066cc;
            --accent-green: #28a745;
            --accent-orange: #fd7e14;
            --accent-purple: #6f42c1;
            --positive-color: #28a745;
            --negative-color: #dc3545;
            --neutral-color: #6c757d;
            --background-light: #f8f9fa;
            --background-white: #ffffff;
            --text-dark: #2f4251;
            --text-muted: #6c757d;
            --border-color: rgba(255, 255, 255, 0.3);
            --border-light: #e9ecef;
            --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 6px rgba(0,0,0,0.1);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-light: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        body {
            font-family: 'Expletus Sans', sans-serif;
            background-color: var(--background-light);
            margin: 0;
            padding: 15px;
            line-height: 1.5;
        }

        .page-header {
            background: var(--gradient-primary);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-medium);
            position: relative;
        }

        .page-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
        }

        .page-header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .page-header .refresh-info {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 12px;
            opacity: 0.8;
        }

        /* Dashboard Summary Section */
        .dashboard-summary {
            background: var(--background-white);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-light);
        }

        .summary-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-metric {
            background: var(--gradient-light);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid var(--primary-color);
            position: relative;
        }

        .summary-metric.positive {
            border-left-color: var(--positive-color);
        }

        .summary-metric.negative {
            border-left-color: var(--negative-color);
        }

        .summary-metric.neutral {
            border-left-color: var(--neutral-color);
        }

        .metric-value {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .metric-value.positive {
            color: var(--positive-color);
        }

        .metric-value.negative {
            color: var(--negative-color);
        }

        .metric-value.neutral {
            color: var(--primary-color);
        }

        .metric-label {
            font-size: 11px;
            color: var(--text-muted);
            text-transform: uppercase;
            font-weight: 500;
        }

        .metric-change {
            font-size: 10px;
            margin-top: 3px;
        }

        /* Tab Navigation */
        .nav-tabs {
            border-bottom: 2px solid var(--primary-color);
            margin-bottom: 20px;
        }

        .nav-tabs .nav-link {
            color: var(--text-dark);
            border: none;
            padding: 10px 20px;
            font-weight: 600;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
            font-size: 14px;
        }

        .nav-tabs .nav-link.active {
            background-color: var(--primary-color);
            color: white;
            border-bottom: 2px solid var(--primary-color);
        }

        .nav-tabs .nav-link:hover {
            background-color: rgba(77, 0, 0, 0.1);
        }

        /* Sub-tab Navigation */
        .sub-nav-tabs {
            border-bottom: 1px solid var(--border-light);
            margin-bottom: 15px;
            background: var(--background-light);
            padding: 5px;
            border-radius: 6px;
        }

        .sub-nav-tabs .nav-link {
            color: var(--text-muted);
            border: none;
            padding: 8px 16px;
            font-weight: 500;
            border-radius: 4px;
            margin-right: 3px;
            font-size: 13px;
            background: transparent;
        }

        .sub-nav-tabs .nav-link.active {
            background-color: white;
            color: var(--primary-color);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .sub-nav-tabs .nav-link:hover:not(.active) {
            background-color: rgba(255,255,255,0.5);
            color: var(--text-dark);
        }

        /* Sub-tab Content */
        .sub-tab-content {
            min-height: 400px;
        }

        .sub-tab-pane {
            display: none;
        }

        .sub-tab-pane.active {
            display: block;
        }

        .sub-tab-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: var(--text-muted);
            flex-direction: column;
            gap: 10px;
        }

        /* Compact Filter Panel */
        .filter-panel {
            background: var(--background-white);
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: var(--shadow-light);
            margin-bottom: 20px;
            border: 1px solid var(--border-light);
        }

        .filter-panel h5 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: 600;
            font-size: 16px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .filter-group {
            flex: 1;
            min-width: 150px;
        }

        .filter-group.date-range {
            min-width: 200px;
        }

        .filter-group.slave-group {
            min-width: 180px;
            max-width: 200px;
        }

        .filter-group.quick-dates-group {
            min-width: 160px;
        }

        .filter-group.actions {
            flex: 0 0 auto;
            min-width: 160px;
        }

        /* Quick dates styling */
        .quick-dates {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .form-control, .form-select {
            border: 1px solid var(--border-light);
            border-radius: 6px;
            padding: 6px 10px;
            font-size: 13px;
            height: 36px;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(77, 0, 0, 0.15);
        }

        .form-label {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 5px;
        }

        /* Enhanced Multi-select Dropdown */
        .multi-select-wrapper {
            position: relative;
            width: 100%;
        }

        .multi-select-trigger {
            width: 100%;
            padding: 6px 30px 6px 10px;
            border: 1px solid var(--border-light);
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 13px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .multi-select-trigger:hover {
            border-color: var(--primary-color);
        }

        .multi-select-trigger.open {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(77, 0, 0, 0.15);
        }

        .multi-select-display {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .multi-select-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            margin-left: 5px;
        }

        .multi-select-arrow {
            position: absolute;
            right: 8px;
            transition: transform 0.3s;
            color: var(--text-muted);
        }

        .multi-select-arrow.open {
            transform: rotate(180deg);
        }

        .multi-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--border-light);
            border-radius: 6px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 250px;
            overflow: hidden;
            display: none;
            margin-top: 2px;
        }

        .multi-select-dropdown.show {
            display: block;
        }

        .multi-select-header {
            padding: 8px;
            border-bottom: 1px solid var(--border-light);
            background: var(--background-light);
        }

        .multi-select-search {
            width: 100%;
            border: 1px solid var(--border-light);
            border-radius: 4px;
            padding: 5px 8px;
            font-size: 12px;
            margin-bottom: 8px;
        }

        .multi-select-actions {
            display: flex;
            gap: 5px;
        }

        .multi-select-action-btn {
            flex: 1;
            padding: 4px 8px;
            font-size: 11px;
            border: 1px solid var(--border-light);
            background: white;
            border-radius: 3px;
            cursor: pointer;
            color: var(--text-muted);
        }

        .multi-select-action-btn:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .multi-select-options {
            max-height: 150px;
            overflow-y: auto;
        }

        .multi-select-option {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .multi-select-option:hover {
            background-color: var(--background-light);
        }

        .multi-select-option.selected {
            background-color: rgba(77, 0, 0, 0.1);
        }

        .multi-select-checkbox {
            margin-right: 8px;
            width: 14px;
            height: 14px;
            accent-color: var(--primary-color);
        }

        .multi-select-label {
            flex: 1;
        }

        .multi-select-option.selected .multi-select-label {
            color: var(--primary-color);
            font-weight: 500;
        }

        /* Button Styles */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 8px 16px;
            font-weight: 600;
            border-radius: 6px;
            font-size: 13px;
            height: 36px;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-secondary {
            background-color: var(--neutral-color);
            border-color: var(--neutral-color);
            padding: 6px 12px;
            font-weight: 500;
            border-radius: 6px;
            font-size: 13px;
            height: 36px;
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: transparent;
            padding: 6px 12px;
            font-weight: 500;
            border-radius: 6px;
            font-size: 13px;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* Quick Date Buttons */
        .quick-dates {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .quick-date-btn {
            padding: 4px 8px;
            font-size: 11px;
            border: 1px solid var(--border-light);
            background: white;
            border-radius: 4px;
            cursor: pointer;
            color: var(--text-muted);
        }

        .quick-date-btn:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Toggle Switch */
        .view-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background-color: var(--border-light);
            border-radius: 15px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .toggle-switch.active {
            background-color: var(--primary-color);
        }

        .toggle-slider {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(30px);
        }

        .toggle-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .toggle-label.active {
            color: var(--primary-color);
        }

        /* Content Cards */
        .content-card {
            background: var(--background-white);
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-light);
        }

        .content-card h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: 600;
            font-size: 18px;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .refresh-btn {
            background: none;
            border: 1px solid var(--border-light);
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            color: var(--text-muted);
            font-size: 12px;
        }

        .refresh-btn:hover {
            background-color: var(--background-light);
            color: var(--primary-color);
        }

        .export-btn {
            background: var(--accent-blue);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 12px;
        }

        .export-btn:hover {
            background-color: #0056b3;
        }

        /* Enhanced Chart Containers with Better Space Utilization */
        .chart-container {
            background: var(--background-white);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            min-height: 400px;
            border: 1px solid var(--border-light);
            position: relative;
            width: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .chart-container.compact {
            min-height: 320px;
            padding: 8px;
        }

        .chart-container.large {
            min-height: 500px;
        }

        .chart-container > div[id$="-chart"] {
            width: 100% !important;
            flex: 1;
            min-height: 350px;
            max-width: 100%;
            overflow: visible;
            display: block;
        }

        .chart-container.compact > div[id$="-chart"] {
            min-height: 280px;
        }

        .chart-container.large > div[id$="-chart"] {
            min-height: 450px;
        }

        /* Enhanced Plotly chart responsiveness */
        .chart-container .js-plotly-plot {
            width: 100% !important;
            height: 100% !important;
            min-height: inherit;
        }

        .chart-container .plotly {
            width: 100% !important;
            height: 100% !important;
            min-height: inherit;
        }

        .chart-container .plotly .main-svg {
            width: 100% !important;
            height: 100% !important;
        }

        /* Legend positioning options */
        .chart-container .plotly .legend {
            max-height: 120px;
            overflow-y: auto;
        }

        .chart-container.legend-bottom .plotly .legend {
            y: -0.2;
        }

        .chart-container.legend-right .plotly .legend {
            x: 1.02;
            y: 1;
        }

        .chart-container.legend-top .plotly .legend {
            y: 1.1;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            flex-wrap: wrap;
            gap: 10px;
            flex-shrink: 0;
            padding: 5px 0;
        }

        .chart-title {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
            flex: 1;
        }

        .chart-controls {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-wrap: wrap;
            flex-shrink: 0;
        }

        .chart-legend-controls {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .legend-position-btn {
            padding: 3px 6px;
            border: 1px solid var(--border-light);
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
            color: var(--text-muted);
        }

        .legend-position-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .legend-position-btn:hover:not(.active) {
            background-color: var(--background-light);
            color: var(--primary-color);
        }

        .chart-type-toggle {
            display: flex;
            border: 1px solid var(--border-light);
            border-radius: 4px;
            overflow: hidden;
        }

        .chart-type-btn {
            padding: 4px 8px;
            border: none;
            background: white;
            cursor: pointer;
            font-size: 11px;
            color: var(--text-muted);
            border-right: 1px solid var(--border-light);
        }

        .chart-type-btn:last-child {
            border-right: none;
        }

        .chart-type-btn.active {
            background-color: var(--primary-color);
            color: white;
        }

        .chart-type-btn:hover:not(.active) {
            background-color: var(--background-light);
        }

        .chart-zoom-controls {
            display: flex;
            gap: 4px;
        }

        .zoom-btn {
            padding: 4px 6px;
            border: 1px solid var(--border-light);
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
            color: var(--text-muted);
        }

        .zoom-btn:hover {
            background-color: var(--background-light);
            color: var(--primary-color);
        }

        /* Enhanced Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 12px;
            margin-bottom: 15px;
        }

        .metric-card {
            background: var(--gradient-light);
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid var(--primary-color);
            position: relative;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .metric-card.positive {
            border-left-color: var(--positive-color);
        }

        .metric-card.negative {
            border-left-color: var(--negative-color);
        }

        .metric-card.neutral {
            border-left-color: var(--neutral-color);
        }

        .metric-value {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .metric-value.positive {
            color: var(--positive-color);
        }

        .metric-value.negative {
            color: var(--negative-color);
        }

        .metric-value.neutral {
            color: var(--primary-color);
        }

        .metric-label {
            font-size: 10px;
            color: var(--text-muted);
            text-transform: uppercase;
            font-weight: 500;
            line-height: 1.2;
        }

        .metric-change {
            font-size: 9px;
            margin-top: 2px;
            font-weight: 500;
        }

        .metric-change.positive {
            color: var(--positive-color);
        }

        .metric-change.negative {
            color: var(--negative-color);
        }

        .metric-tooltip {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: var(--text-muted);
            color: white;
            font-size: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: help;
        }

        /* Enhanced Data Tables with Column Filtering */
        .data-table-container {
            background: var(--background-white);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-light);
            margin-bottom: 15px;
        }

        .data-table-header {
            background: var(--gradient-light);
            padding: 10px 15px;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .data-table-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .data-table-actions {
            display: flex;
            gap: 8px;
        }

        .table-action-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-light);
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            color: var(--text-muted);
        }

        .table-action-btn:hover {
            background-color: var(--background-light);
            color: var(--primary-color);
        }

        /* Column Filter Controls */
        .column-filters {
            background: #f8f9fa;
            border-bottom: 1px solid var(--border-light);
            padding: 8px;
        }

        .filter-row {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .column-filter {
            flex: 1;
            position: relative;
        }

        .filter-input {
            width: 100%;
            padding: 4px 6px;
            border: 1px solid var(--border-light);
            border-radius: 3px;
            font-size: 11px;
            background: white;
        }

        .filter-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        .filter-dropdown-trigger {
            position: absolute;
            right: 2px;
            top: 2px;
            bottom: 2px;
            width: 20px;
            background: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: var(--text-muted);
        }

        .filter-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--border-light);
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 100;
            display: none;
            margin-top: 2px;
        }

        .filter-dropdown.show {
            display: block;
        }

        .filter-option {
            padding: 6px 8px;
            cursor: pointer;
            font-size: 11px;
            border-bottom: 1px solid var(--border-light);
        }

        .filter-option:hover {
            background-color: var(--background-light);
        }

        .filter-option:last-child {
            border-bottom: none;
        }

        /* Filter Breadcrumbs */
        .filter-breadcrumbs {
            padding: 8px 15px;
            background: #e3f2fd;
            border-bottom: 1px solid var(--border-light);
            display: none;
        }

        .filter-breadcrumbs.show {
            display: block;
        }

        .breadcrumb-item {
            display: inline-flex;
            align-items: center;
            background: white;
            padding: 3px 8px;
            border-radius: 12px;
            margin-right: 5px;
            margin-bottom: 3px;
            font-size: 11px;
            border: 1px solid var(--border-light);
        }

        .breadcrumb-remove {
            margin-left: 5px;
            cursor: pointer;
            color: var(--text-muted);
        }

        .breadcrumb-remove:hover {
            color: var(--negative-color);
        }

        .clear-all-filters {
            background: var(--negative-color);
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
            margin-left: 10px;
        }

        .data-table {
            max-height: 300px;
            overflow-y: auto;
        }

        .data-table table {
            width: 100%;
            margin: 0;
            font-size: 12px;
        }

        .data-table thead {
            background-color: var(--primary-color);
            color: white;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table th {
            padding: 8px 10px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 10px;
            text-align: left;
            border-right: 1px solid rgba(255,255,255,0.2);
            cursor: pointer;
            position: relative;
        }

        .data-table th:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .data-table th:last-child {
            border-right: none;
        }

        .sort-indicator {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 8px;
        }

        .data-table td {
            padding: 6px 10px;
            border-bottom: 1px solid var(--border-light);
            border-right: 1px solid var(--border-light);
        }

        .data-table td:last-child {
            border-right: none;
        }

        .data-table tbody tr:hover {
            background-color: rgba(77, 0, 0, 0.05);
        }

        .data-table tbody tr:nth-child(even) {
            background-color: rgba(248, 249, 250, 0.5);
        }

        /* Value formatting */
        .value-positive {
            color: var(--positive-color);
            font-weight: 600;
        }

        .value-negative {
            color: var(--negative-color);
            font-weight: 600;
        }

        .value-neutral {
            color: var(--text-dark);
        }

        .value-large {
            font-weight: 600;
        }

        /* Pagination */
        .table-pagination {
            padding: 10px 15px;
            background: var(--background-light);
            border-top: 1px solid var(--border-light);
            display: flex;
            justify-content: between;
            align-items: center;
            font-size: 12px;
        }

        .pagination-info {
            color: var(--text-muted);
        }

        .pagination-controls {
            display: flex;
            gap: 5px;
        }

        .pagination-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-light);
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            color: var(--text-muted);
        }

        .pagination-btn:hover:not(:disabled) {
            background-color: var(--primary-color);
            color: white;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Collapsible Sections */
        .collapsible-section {
            border: 1px solid var(--border-light);
            border-radius: 8px;
            margin-bottom: 15px;
            background: var(--background-white);
        }

        .collapsible-header {
            background: var(--gradient-light);
            padding: 10px 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 8px 8px 0 0;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .collapsible-header:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
        }

        .collapsible-content {
            padding: 15px;
            display: none;
            border-top: 1px solid var(--border-light);
        }

        .collapsible-content.active {
            display: block;
        }

        .expand-icon {
            transition: transform 0.3s;
            color: var(--text-muted);
        }

        .expand-icon.rotated {
            transform: rotate(180deg);
        }

        /* Raw Data Section */
        .raw-data-section {
            background: var(--background-light);
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
        }

        .raw-data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .raw-data-title {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .raw-data-stats {
            font-size: 11px;
            color: var(--text-muted);
        }

        /* Statistical Summary */
        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin-bottom: 10px;
        }

        .stat-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid var(--border-light);
        }

        .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 10px;
            color: var(--text-muted);
            text-transform: uppercase;
        }

        /* Info Tooltips */
        .info-tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .info-tooltip .tooltip-content {
            visibility: hidden;
            width: 200px;
            background-color: var(--text-dark);
            color: white;
            text-align: left;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            font-size: 11px;
            line-height: 1.3;
        }

        .info-tooltip:hover .tooltip-content {
            visibility: visible;
        }

        .info-tooltip .tooltip-content::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: var(--text-dark) transparent transparent transparent;
        }

        /* Loading States */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: var(--text-muted);
            flex-direction: column;
            gap: 10px;
        }

        .spinner {
            border: 3px solid var(--border-light);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 12px;
            color: var(--text-muted);
        }

        /* Progress Bar */
        .progress-container {
            width: 100%;
            background-color: var(--border-light);
            border-radius: 4px;
            overflow: hidden;
            height: 4px;
            margin-top: 10px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Status Indicators */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 11px;
            padding: 3px 6px;
            border-radius: 12px;
            font-weight: 500;
        }

        .status-indicator.success {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--positive-color);
        }

        .status-indicator.warning {
            background-color: rgba(253, 126, 20, 0.1);
            color: var(--accent-orange);
        }

        .status-indicator.error {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--negative-color);
        }

        .status-indicator.info {
            background-color: rgba(0, 102, 204, 0.1);
            color: var(--accent-blue);
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: currentColor;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .summary-metrics {
                grid-template-columns: repeat(3, 1fr);
            }

            .metrics-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .chart-container {
                min-height: 350px;
            }

            .chart-container.compact {
                min-height: 280px;
            }

            .chart-container.large {
                min-height: 400px;
            }

            .chart-container > div[id$="-chart"] {
                min-height: 300px;
            }

            .chart-container.compact > div[id$="-chart"] {
                min-height: 230px;
            }

            .chart-container.large > div[id$="-chart"] {
                min-height: 350px;
            }

            /* Adjust legend controls for smaller screens */
            .chart-legend-controls {
                flex-wrap: wrap;
                gap: 2px;
            }

            .legend-position-btn {
                font-size: 9px;
                padding: 2px 4px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .summary-metrics {
                grid-template-columns: repeat(2, 1fr);
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .filter-row {
                flex-direction: column;
                gap: 10px;
            }

            .filter-group {
                min-width: auto;
            }

            .chart-header {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .chart-controls {
                justify-content: center;
                flex-wrap: wrap;
            }

            .chart-legend-controls {
                justify-content: center;
                margin-top: 5px;
            }

            .nav-tabs .nav-link {
                padding: 8px 12px;
                font-size: 12px;
            }

            .chart-container {
                min-height: 320px;
                padding: 8px;
            }

            .chart-container.compact {
                min-height: 260px;
            }

            .chart-container.large {
                min-height: 380px;
            }

            .chart-container > div[id$="-chart"] {
                min-height: 260px;
            }

            .chart-container.compact > div[id$="-chart"] {
                min-height: 200px;
            }

            .chart-container.large > div[id$="-chart"] {
                min-height: 320px;
            }

            /* Force legend to bottom on mobile for better space utilization */
            .chart-container.legend-right .plotly .legend,
            .chart-container.legend-top .plotly .legend {
                /* Mobile devices should use bottom legend for better space utilization */
                position: relative;
            }
        }

        @media (max-width: 480px) {
            .summary-metrics {
                grid-template-columns: 1fr;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .quick-dates {
                justify-content: center;
            }

            .chart-container {
                min-height: 250px;
                padding: 8px;
            }

            .chart-container > div[id$="-chart"] {
                height: 200px !important;
                min-height: 200px;
            }

            .chart-header {
                margin-bottom: 10px;
            }

            .chart-title {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fa fa-line-chart"></i> Advanced Slippage Analysis</h1>
            <p>Comprehensive slippage monitoring and trade comparison dashboard</p>
        </div>

        <!-- Dashboard Summary -->
        <div class="dashboard-summary">
            <div class="summary-metrics">
                <div class="summary-metric positive">
                    <div class="metric-value positive">-₹45,230</div>
                    <div class="metric-label">Total Slippage (Favorable)</div>
                    <div class="metric-change positive">↓ 12.5% vs last month</div>
                </div>
                <div class="summary-metric negative">
                    <div class="metric-value negative">₹23,450</div>
                    <div class="metric-label">Execution Slippage</div>
                    <div class="metric-change negative">↑ 8.3% vs last month</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral">2.34s</div>
                    <div class="metric-label">Avg Execution Time</div>
                    <div class="metric-change positive">↓ 0.2s vs last month</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral">₹12.5Cr</div>
                    <div class="metric-label">Total Turnover</div>
                    <div class="metric-change positive">↑ 15.2% vs last month</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral">1,247</div>
                    <div class="metric-label">Total Trades</div>
                    <div class="metric-change positive">↑ 23 vs last month</div>
                </div>
                <div class="summary-metric neutral">
                    <div class="metric-value neutral">-18.5 BPS</div>
                    <div class="metric-label">Slip-to-Turnover Ratio</div>
                    <div class="metric-change positive">↓ 2.1 BPS vs last month</div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="slippage-tab" data-target="slippage-panel" type="button" role="tab">
                    <i class="fa fa-bar-chart"></i> Slippage Monitoring
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="comparison-tab" data-target="comparison-panel" type="button" role="tab">
                    <i class="fa fa-exchange"></i> Trade Vision
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analytics-tab" data-target="analytics-panel" type="button" role="tab">
                    <i class="fa fa-dashboard"></i> Combined Analytics
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Slippage Monitoring Panel -->
            <div class="tab-pane show active" id="slippage-panel" role="tabpanel">
                <!-- Enhanced Filter Panel -->
                <div class="filter-panel">
                    <h5><i class="fa fa-filter"></i> Analysis Filters</h5>

                    <!-- First Row: Date Range, Segment, Strategy/Cluster -->
                    <div class="filter-row">
                        <div class="filter-group date-range">
                            <label class="form-label">Date Range</label>
                            <div class="d-flex gap-2">
                                <input type="date" class="form-control" id="start-date" value="2024-01-01">
                                <input type="date" class="form-control" id="end-date" value="2024-01-31">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Segment</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="segment-dropdown">
                                    <span class="multi-select-display">Select segments...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="segment-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search segments...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="OPTIDX">
                                            <input type="checkbox" class="multi-select-checkbox" checked>
                                            <span class="multi-select-label">OPTIDX</span>
                                        </div>
                                        <div class="multi-select-option" data-value="OPTIDX_BSE">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">OPTIDX_BSE</span>
                                        </div>
                                        <div class="multi-select-option" data-value="OPTIDX_US">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">OPTIDX_US</span>
                                        </div>
                                        <div class="multi-select-option" data-value="OPTSTK">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">OPTSTK</span>
                                        </div>
                                        <div class="multi-select-option" data-value="FUTSTK">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">FUTSTK</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Strategy/Cluster</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="strategy-dropdown">
                                    <span class="multi-select-display">Select strategies...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="strategy-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search strategies...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="strategy_alpha_v1">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">strategy_alpha_v1</span>
                                        </div>
                                        <div class="multi-select-option" data-value="strategy_beta_v2">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">strategy_beta_v2</span>
                                        </div>
                                        <div class="multi-select-option" data-value="strategy_gamma_v1">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">strategy_gamma_v1</span>
                                        </div>
                                        <div class="multi-select-option" data-value="cluster_izmir">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">cluster_izmir</span>
                                        </div>
                                        <div class="multi-select-option" data-value="cluster_kari">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">cluster_kari</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Exchange</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="exchange-dropdown">
                                    <span class="multi-select-display">Select exchanges...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="exchange-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search exchanges...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="IND">
                                            <input type="checkbox" class="multi-select-checkbox" checked>
                                            <span class="multi-select-label">IND (India Combined)</span>
                                        </div>
                                        <div class="multi-select-option" data-value="NSE">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">NSE (National Stock Exchange)</span>
                                        </div>
                                        <div class="multi-select-option" data-value="BSE">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">BSE (Bombay Stock Exchange)</span>
                                        </div>
                                        <div class="multi-select-option" data-value="US">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">US (United States)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Second Row: Quick Dates, Slave Names, Load Slaves, Run Analysis -->
                    <div class="filter-row">
                        <div class="filter-group quick-dates-group">
                            <label class="form-label">Quick Dates</label>
                            <div class="quick-dates">
                                <button class="quick-date-btn" data-days="7">7d</button>
                                <button class="quick-date-btn" data-days="30">30d</button>
                                <button class="quick-date-btn" data-days="90">90d</button>
                                <button class="quick-date-btn" data-days="365">1y</button>
                            </div>
                        </div>

                        <div class="filter-group slave-group">
                            <label class="form-label">Slave Names</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="slave-dropdown">
                                    <span class="multi-select-display">Select slaves...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="slave-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search slaves...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="slave_001">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_001</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_002">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_002</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_003">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_003</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_hedge_001">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_hedge_001</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_hedge_002">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_hedge_002</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group actions">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" id="load-slaves-btn">
                                    <i class="fa fa-refresh"></i> Load Slaves
                                </button>
                                <button class="btn btn-primary" id="run-analysis-btn">
                                    <i class="fa fa-play"></i> Run Analysis
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Sub-tab Navigation -->
                <ul class="nav sub-nav-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-subtab" data-bs-toggle="tab" data-bs-target="#overview-content" type="button" role="tab">
                            <i class="fa fa-dashboard"></i> Overview
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="strategy-subtab" data-bs-toggle="tab" data-bs-target="#strategy-content" type="button" role="tab">
                            <i class="fa fa-line-chart"></i> Strategy Analysis
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="slave-subtab" data-bs-toggle="tab" data-bs-target="#slave-content" type="button" role="tab">
                            <i class="fa fa-bar-chart"></i> Slave Analysis
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="timing-subtab" data-bs-toggle="tab" data-bs-target="#timing-content" type="button" role="tab">
                            <i class="fa fa-clock-o"></i> Timing Analysis
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="risk-subtab" data-bs-toggle="tab" data-bs-target="#risk-content" type="button" role="tab">
                            <i class="fa fa-shield"></i> Risk Analysis
                        </button>
                    </li>
                </ul>

                <!-- Sub-tab Content -->
                <div class="sub-tab-content">
                    <!-- Overview Sub-tab -->
                    <div class="sub-tab-pane active" id="overview-content" role="tabpanel">
                        <div class="content-card">
                            <h4>
                                Overview Dashboard
                                <div class="card-actions">
                                    <div class="view-toggle">
                                        <span class="toggle-label" id="cluster-label">Cluster View</span>
                                        <div class="toggle-switch" id="view-toggle-switch">
                                            <div class="toggle-slider"></div>
                                        </div>
                                        <span class="toggle-label active" id="slave-label">Slave View</span>
                                    </div>
                                    <button class="refresh-btn" title="Refresh Data">
                                        <i class="fa fa-refresh"></i> Refresh
                                    </button>
                                    <button class="export-btn" title="Export Data">
                                        <i class="fa fa-download"></i> Export
                                    </button>
                                </div>
                            </h4>

                        <!-- Interactive Charts with Enhanced Controls -->
                        <div class="chart-container large legend-bottom">
                            <div class="chart-header">
                                <div class="chart-title">Multi-Strategy/Slave Slippage Trend</div>
                                <div class="chart-controls">
                                    <div class="chart-type-toggle">
                                        <button class="chart-type-btn active" data-chart="trend">Trend</button>
                                        <button class="chart-type-btn" data-chart="daily">Daily</button>
                                        <button class="chart-type-btn" data-chart="hourly">Hourly</button>
                                    </div>
                                    <div class="chart-legend-controls">
                                        <span style="font-size: 10px; color: var(--text-muted);">Legend:</span>
                                        <button class="legend-position-btn" data-position="bottom">Bottom</button>
                                        <button class="legend-position-btn active" data-position="right">Right</button>
                                        <button class="legend-position-btn" data-position="top">Top</button>
                                    </div>
                                    <div class="chart-zoom-controls">
                                        <button class="zoom-btn" title="Zoom In"><i class="fa fa-search-plus"></i></button>
                                        <button class="zoom-btn" title="Zoom Out"><i class="fa fa-search-minus"></i></button>
                                        <button class="zoom-btn" title="Reset Zoom"><i class="fa fa-refresh"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div id="slippage-trend-chart" class="loading">
                                <div class="spinner"></div>
                                <div class="loading-text">Loading multi-strategy chart data...</div>
                                <div class="progress-container">
                                    <div class="progress-bar" style="width: 45%;"></div>
                                </div>
                            </div>
                        </div>

                            <!-- Key Metrics Summary -->
                            <div class="metrics-grid">
                                <div class="metric-card positive">
                                    <div class="metric-value positive">-₹45,230</div>
                                    <div class="metric-label">Total Slippage</div>
                                    <div class="metric-change positive">↓ 12.5% vs last period</div>
                                </div>
                                <div class="metric-card neutral">
                                    <div class="metric-value neutral">1,247</div>
                                    <div class="metric-label">Total Trades</div>
                                    <div class="metric-change positive">↑ 23 vs last period</div>
                                </div>
                                <div class="metric-card neutral">
                                    <div class="metric-value neutral">2.34s</div>
                                    <div class="metric-label">Avg Execution Time</div>
                                    <div class="metric-change positive">↓ 0.2s improvement</div>
                                </div>
                                <div class="metric-card neutral">
                                    <div class="metric-value neutral">-18.5 BPS</div>
                                    <div class="metric-label">Slip-to-Turnover Ratio</div>
                                    <div class="metric-change positive">↓ 2.1 BPS improvement</div>
                                </div>
                            </div>

                            <!-- Enhanced Overview Chart -->
                            <div class="chart-container compact legend-right">
                                <div class="chart-header">
                                    <div class="chart-title">Strategy Performance Overview</div>
                                    <div class="chart-controls">
                                        <div class="chart-type-toggle">
                                            <button class="chart-type-btn active" data-chart="trend">Trend</button>
                                            <button class="chart-type-btn" data-chart="daily">Daily</button>
                                            <button class="chart-type-btn" data-chart="comparison">Compare</button>
                                        </div>
                                        <div class="chart-legend-controls">
                                            <button class="legend-position-btn" data-position="bottom">Bottom</button>
                                            <button class="legend-position-btn active" data-position="right">Right</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="overview-chart" class="loading">
                                    <div class="spinner"></div>
                                    <div class="loading-text">Loading strategy overview...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Strategy Analysis Sub-tab -->
                    <div class="sub-tab-pane" id="strategy-content" role="tabpanel">
                        <div class="content-card">
                            <h4>
                                Strategy-Level Analysis with Advanced Filtering
                                <div class="card-actions">
                                    <div class="info-tooltip">
                                        <i class="fa fa-info-circle"></i>
                                        <div class="tooltip-content">
                                            Strategy-level analysis compares performance across different trading strategies.
                                            Use filters to drill down into specific segments, time periods, or performance metrics.
                                        </div>
                                    </div>
                                    <button class="refresh-btn" title="Refresh Strategy Data">
                                        <i class="fa fa-refresh"></i> Refresh
                                    </button>
                                    <button class="export-btn" title="Export Strategy Analysis">
                                        <i class="fa fa-download"></i> Export
                                    </button>
                                </div>
                            </h4>

                            <!-- Strategy Performance Metrics -->
                            <div class="metrics-grid">
                                <div class="metric-card positive">
                                    <div class="metric-value positive">3</div>
                                    <div class="metric-label">Profitable Strategies</div>
                                    <div class="metric-change positive">↑ 1 vs last period</div>
                                </div>
                                <div class="metric-card neutral">
                                    <div class="metric-value neutral">-₹21,880</div>
                                    <div class="metric-label">Best Strategy Slippage</div>
                                    <div class="metric-change positive">strategy_alpha_v1</div>
                                </div>
                                <div class="metric-card negative">
                                    <div class="metric-value negative">₹8,750</div>
                                    <div class="metric-label">Worst Strategy Slippage</div>
                                    <div class="metric-change negative">strategy_beta_v2</div>
                                </div>
                                <div class="metric-card neutral">
                                    <div class="metric-value neutral">-8.2 BPS</div>
                                    <div class="metric-label">Avg Strategy Performance</div>
                                    <div class="metric-change positive">↓ 1.5 BPS improvement</div>
                                </div>
                            </div>

                            <!-- Strategy Comparison Chart -->
                            <div class="chart-container large legend-right">
                                <div class="chart-header">
                                    <div class="chart-title">Strategy Performance Comparison</div>
                                    <div class="chart-controls">
                                        <div class="chart-type-toggle">
                                            <button class="chart-type-btn active" data-chart="bar">Bar</button>
                                            <button class="chart-type-btn" data-chart="line">Line</button>
                                            <button class="chart-type-btn" data-chart="scatter">Scatter</button>
                                        </div>
                                        <div class="chart-legend-controls">
                                            <span style="font-size: 10px; color: var(--text-muted);">Legend:</span>
                                            <button class="legend-position-btn" data-position="bottom">Bottom</button>
                                            <button class="legend-position-btn active" data-position="right">Right</button>
                                            <button class="legend-position-btn" data-position="top">Top</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="strategy-comparison-chart"></div>
                            </div>

                            <!-- Enhanced Data Table with Column Filtering -->
                            <div class="data-table-container">
                                <div class="data-table-header">
                                    <div class="data-table-title">Strategy Summary Data</div>
                                    <div class="data-table-actions">
                                        <button class="table-action-btn" title="Clear All Filters">
                                            <i class="fa fa-times"></i> Clear Filters
                                        </button>
                                        <button class="table-action-btn" title="Export to Excel">
                                            <i class="fa fa-file-excel-o"></i> Excel
                                        </button>
                                        <button class="table-action-btn" title="Export to CSV">
                                            <i class="fa fa-file-text-o"></i> CSV
                                        </button>
                                    </div>
                                </div>

                                <!-- Filter Breadcrumbs -->
                                <div class="filter-breadcrumbs">
                                    <span>Active Filters:</span>
                                    <span class="breadcrumb-item">
                                        Strategy: contains "alpha"
                                        <i class="fa fa-times breadcrumb-remove"></i>
                                    </span>
                                    <span class="breadcrumb-item">
                                        Slippage: < 0
                                        <i class="fa fa-times breadcrumb-remove"></i>
                                    </span>
                                    <button class="clear-all-filters">Clear All</button>
                                </div>

                                <!-- Column Filters -->
                                <div class="column-filters">
                                    <div class="filter-row">
                                        <div class="column-filter">
                                            <input type="text" class="filter-input" placeholder="Filter strategy..." value="alpha">
                                            <button class="filter-dropdown-trigger">
                                                <i class="fa fa-caret-down"></i>
                                            </button>
                                            <div class="filter-dropdown">
                                                <div class="filter-option">Contains</div>
                                                <div class="filter-option">Equals</div>
                                                <div class="filter-option">Starts with</div>
                                                <div class="filter-option">Ends with</div>
                                            </div>
                                        </div>
                                        <div class="column-filter">
                                            <input type="text" class="filter-input" placeholder="Filter slippage..." value="< 0">
                                            <button class="filter-dropdown-trigger">
                                                <i class="fa fa-caret-down"></i>
                                            </button>
                                            <div class="filter-dropdown">
                                                <div class="filter-option">Equals</div>
                                                <div class="filter-option">Greater than</div>
                                                <div class="filter-option">Less than</div>
                                                <div class="filter-option">Between</div>
                                            </div>
                                        </div>
                                        <div class="column-filter">
                                            <input type="text" class="filter-input" placeholder="Filter execution...">
                                            <button class="filter-dropdown-trigger">
                                                <i class="fa fa-caret-down"></i>
                                            </button>
                                        </div>
                                        <div class="column-filter">
                                            <input type="text" class="filter-input" placeholder="Filter execution...">
                                            <button class="filter-dropdown-trigger">
                                                <i class="fa fa-caret-down"></i>
                                            </button>
                                        </div>
                                        <div class="column-filter">
                                            <input type="text" class="filter-input" placeholder="Filter turnover...">
                                            <button class="filter-dropdown-trigger">
                                                <i class="fa fa-caret-down"></i>
                                            </button>
                                        </div>
                                        <div class="column-filter">
                                            <input type="text" class="filter-input" placeholder="Filter BPS...">
                                            <button class="filter-dropdown-trigger">
                                                <i class="fa fa-caret-down"></i>
                                            </button>
                                        </div>
                                        <div class="column-filter">
                                            <input type="text" class="filter-input" placeholder="Filter count...">
                                            <button class="filter-dropdown-trigger">
                                                <i class="fa fa-caret-down"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-table">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>
                                                    Strategy Name
                                                    <i class="fa fa-sort sort-indicator"></i>
                                                </th>
                                                <th>
                                                    Total Slippage
                                                    <i class="fa fa-sort-desc sort-indicator"></i>
                                                </th>
                                                <th>
                                                    Execution Signal
                                                    <i class="fa fa-sort sort-indicator"></i>
                                                </th>
                                                <th>
                                                    Execution Slippage
                                                    <i class="fa fa-sort sort-indicator"></i>
                                                </th>
                                                <th>
                                                    Turnover
                                                    <i class="fa fa-sort sort-indicator"></i>
                                                </th>
                                                <th>
                                                    Slip-to-Turnover (BPS)
                                                    <i class="fa fa-sort sort-indicator"></i>
                                                </th>
                                                <th>
                                                    Trade Count
                                                    <i class="fa fa-sort sort-indicator"></i>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>strategy_alpha_v1</td>
                                                <td class="value-positive">-₹12,450</td>
                                                <td class="value-positive">-₹8,230</td>
                                                <td class="value-negative">-₹4,220</td>
                                                <td class="value-large">₹24.5L</td>
                                                <td class="value-positive">-20.3</td>
                                                <td>456</td>
                                            </tr>
                                            <tr>
                                                <td>strategy_gamma_v1</td>
                                                <td class="value-positive">-₹3,200</td>
                                                <td class="value-positive">-₹1,850</td>
                                                <td class="value-positive">-₹1,350</td>
                                                <td class="value-large">₹9.8L</td>
                                                <td class="value-positive">-16.5</td>
                                                <td>189</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-pagination">
                                    <div class="pagination-info">Showing 2 of 12 strategies (filtered)</div>
                                    <div class="pagination-controls">
                                        <button class="pagination-btn" disabled>‹ Prev</button>
                                        <button class="pagination-btn">1</button>
                                        <button class="pagination-btn" disabled>Next ›</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Slave Analysis Sub-tab -->
                    <div class="sub-tab-pane" id="slave-content" role="tabpanel">
                        <div class="sub-tab-loading">
                            <div class="spinner"></div>
                            <div class="loading-text">Loading Slave Analysis...</div>
                            <div style="font-size: 12px; color: var(--text-muted); margin-top: 10px;">
                                Analyzing individual slave performance and execution patterns
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Timing Analysis Sub-tab -->
                    <div class="sub-tab-pane" id="timing-content" role="tabpanel">
                        <div class="sub-tab-loading">
                            <div class="spinner"></div>
                            <div class="loading-text">Loading Timing Analysis...</div>
                            <div style="font-size: 12px; color: var(--text-muted); margin-top: 10px;">
                                Analyzing execution timing patterns and latency distributions
                            </div>
                        </div>
                    </div>

                    <!-- Risk Analysis Sub-tab -->
                    <div class="sub-tab-pane" id="risk-content" role="tabpanel">
                        <div class="sub-tab-loading">
                            <div class="spinner"></div>
                            <div class="loading-text">Loading Risk Analysis...</div>
                            <div style="font-size: 12px; color: var(--text-muted); margin-top: 10px;">
                                Analyzing risk metrics and exposure patterns
                            </div>
                        </div>
                    </div>
                </div>



                    <!-- Slave-Level Analysis -->
                    <div class="content-card">
                        <h4>
                            Slave-Level Slippage Summary
                            <div class="card-actions">
                                <div class="info-tooltip">
                                    <i class="fa fa-info-circle"></i>
                                    <div class="tooltip-content">
                                        Slave-level analysis shows individual execution performance.
                                        Negative values indicate favorable slippage.
                                        BPS = Basis Points (1 BPS = 0.01%)
                                    </div>
                                </div>
                                <button class="refresh-btn" title="Refresh Data">
                                    <i class="fa fa-refresh"></i>
                                </button>
                                <button class="export-btn" title="Export Data">
                                    <i class="fa fa-download"></i>
                                </button>
                            </div>
                        </h4>

                        <div class="chart-container">
                            <div class="chart-header">
                                <div class="chart-title">Slippage Comparison by Slave</div>
                                <div class="chart-controls">
                                    <div class="chart-type-toggle">
                                        <button class="chart-type-btn active" data-chart="bar">Bar</button>
                                        <button class="chart-type-btn" data-chart="line">Line</button>
                                        <button class="chart-type-btn" data-chart="scatter">Scatter</button>
                                    </div>
                                </div>
                            </div>
                            <div id="slave-comparison-chart" class="loading">
                                <div class="spinner"></div>
                                <div class="loading-text">Loading chart data...</div>
                            </div>
                        </div>

                        <!-- Enhanced Collapsible Sections -->
                        <div class="collapsible-section">
                            <div class="collapsible-header" onclick="toggleSection(this)">
                                <span><strong>DTE Analysis</strong>
                                    <span class="status-indicator info">
                                        <span class="status-dot"></span>
                                        Days to Expiry breakdown
                                    </span>
                                </span>
                                <i class="fa fa-chevron-down expand-icon"></i>
                            </div>
                            <div class="collapsible-content">
                                <div class="data-table-container">
                                    <div class="data-table-header">
                                        <div class="data-table-title">DTE Breakdown by Slave</div>
                                        <div class="data-table-actions">
                                            <button class="table-action-btn" title="Show Methodology">
                                                <i class="fa fa-question-circle"></i> Info
                                            </button>
                                            <button class="table-action-btn" title="Export">
                                                <i class="fa fa-download"></i> Export
                                            </button>
                                        </div>
                                    </div>
                                    <div class="data-table">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>Slave Name</th>
                                                    <th>DTE</th>
                                                    <th>Order Type</th>
                                                    <th>Total Slippage</th>
                                                    <th>Slip-to-Turnover (BPS)</th>
                                                    <th>Trade Count</th>
                                                    <th>Avg Trade Size</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>slave_001</td>
                                                    <td>0</td>
                                                    <td>Buy</td>
                                                    <td class="value-positive">-₹2,450</td>
                                                    <td class="value-positive">-12.5</td>
                                                    <td>45</td>
                                                    <td>₹54,444</td>
                                                </tr>
                                                <tr>
                                                    <td>slave_001</td>
                                                    <td>1</td>
                                                    <td>Sell</td>
                                                    <td class="value-negative">₹1,230</td>
                                                    <td class="value-negative">+8.7</td>
                                                    <td>32</td>
                                                    <td>₹38,438</td>
                                                </tr>
                                                <tr>
                                                    <td>slave_002</td>
                                                    <td>0</td>
                                                    <td>Buy</td>
                                                    <td class="value-positive">-₹3,120</td>
                                                    <td class="value-positive">-15.2</td>
                                                    <td>67</td>
                                                    <td>₹46,567</td>
                                                </tr>
                                                <tr>
                                                    <td>slave_hedge_001</td>
                                                    <td>0</td>
                                                    <td>Buy</td>
                                                    <td class="value-positive">-₹890</td>
                                                    <td class="value-positive">-22.1</td>
                                                    <td>23</td>
                                                    <td>₹38,696</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="collapsible-section">
                            <div class="collapsible-header" onclick="toggleSection(this)">
                                <span><strong>Hedge vs Non-Hedge Analysis</strong>
                                    <span class="status-indicator warning">
                                        <span class="status-dot"></span>
                                        Performance comparison
                                    </span>
                                </span>
                                <i class="fa fa-chevron-down expand-icon"></i>
                            </div>
                            <div class="collapsible-content">
                                <div class="metrics-grid">
                                    <div class="metric-card positive">
                                        <div class="metric-value positive">-₹8,450</div>
                                        <div class="metric-label">Hedge Trades Slippage</div>
                                        <div class="metric-tooltip">?</div>
                                    </div>
                                    <div class="metric-card negative">
                                        <div class="metric-value negative">₹12,340</div>
                                        <div class="metric-label">Non-Hedge Slippage</div>
                                        <div class="metric-tooltip">?</div>
                                    </div>
                                    <div class="metric-card neutral">
                                        <div class="metric-value neutral">156</div>
                                        <div class="metric-label">Hedge Trade Count</div>
                                        <div class="metric-tooltip">?</div>
                                    </div>
                                    <div class="metric-card neutral">
                                        <div class="metric-value neutral">891</div>
                                        <div class="metric-label">Non-Hedge Count</div>
                                        <div class="metric-tooltip">?</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timing Analysis -->
                    <div class="content-card">
                        <h4>Strategy Timing Distribution</h4>
                        
                        <div class="chart-container">
                            <div class="chart-title">Execution Timing Histograms</div>
                            <div id="timing-histogram-chart" class="loading">
                                <div class="spinner"></div>
                                <span>Loading chart data...</span>
                            </div>
                        </div>

                        <!-- Timing Stats -->
                        <div class="collapsible-section">
                            <div class="collapsible-header" onclick="toggleSection(this)">
                                <span><strong>Detailed Timing Statistics</strong></span>
                                <i class="fa fa-chevron-down expand-icon"></i>
                            </div>
                            <div class="collapsible-content">
                                <div class="metrics-grid">
                                    <div class="metric-card">
                                        <div class="metric-value">2.34</div>
                                        <div class="metric-label">Avg Signal Time (sec)</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">4.67</div>
                                        <div class="metric-label">Avg Execution Time (sec)</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">2.33</div>
                                        <div class="metric-label">Avg Execution Delay (sec)</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-value">1.89</div>
                                        <div class="metric-label">Median Execution Delay (sec)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Trade Vision Panel -->
            <div class="tab-pane fade" id="comparison-panel" role="tabpanel">
                <!-- Trade Vision Introduction -->
                <div class="content-card">
                    <h4>
                        <i class="fa fa-exchange"></i> Trade Vision - Advanced Strategy Comparison
                        <div class="card-actions">
                            <div class="info-tooltip">
                                <i class="fa fa-info-circle"></i>
                                <div class="tooltip-content">
                                    Trade Vision provides comprehensive comparison between live strategy performance,
                                    cluster backtests, and historical data. It helps identify performance gaps,
                                    execution differences, and optimization opportunities.
                                </div>
                            </div>
                        </div>
                    </h4>
                    <div class="row">
                        <div class="col-md-8">
                            <p class="text-muted mb-3">
                                <strong>What Trade Vision Does:</strong><br>
                                • <strong>Strategy vs Cluster Comparison:</strong> Compare live strategy performance against cluster backtests<br>
                                • <strong>Exit Count Analysis:</strong> Analyze differences in trade exit patterns and timing<br>
                                • <strong>PnL Attribution:</strong> Break down PnL differences between live and backtest scenarios<br>
                                • <strong>Holding Time Distribution:</strong> Compare trade duration patterns with KDE analysis<br>
                                • <strong>Short Duration Trade Detection:</strong> Identify and analyze rapid entry/exit patterns
                            </p>
                        </div>
                        <div class="col-md-4">
                            <div class="metrics-grid">
                                <div class="metric-card info">
                                    <div class="metric-value neutral">Live vs Backtest</div>
                                    <div class="metric-label">Primary Comparison</div>
                                </div>
                                <div class="metric-card info">
                                    <div class="metric-value neutral">Multi-Dimensional</div>
                                    <div class="metric-label">Analysis Type</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Comparison Filters -->
                <div class="filter-panel">
                    <h5><i class="fa fa-filter"></i> Trade Vision Comparison Filters</h5>

                    <!-- First Row: Date Range, Strategy Selection -->
                    <div class="filter-row">
                        <div class="filter-group date-range">
                            <label class="form-label">Date Range</label>
                            <div class="d-flex gap-2">
                                <input type="date" class="form-control" id="tv-start-date" value="2024-01-01">
                                <input type="date" class="form-control" id="tv-end-date" value="2024-01-31">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Primary Strategy</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="tv-strategy-dropdown">
                                    <span class="multi-select-display">Select primary strategy...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="tv-strategy-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search strategies...">
                                        <div class="multi-select-actions">
                                            <button type="button" class="multi-select-action-btn select-all">Select All</button>
                                            <button type="button" class="multi-select-action-btn clear-all">Clear All</button>
                                        </div>
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="strategy_alpha_v1">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">strategy_alpha_v1</span>
                                        </div>
                                        <div class="multi-select-option" data-value="strategy_beta_v2">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">strategy_beta_v2</span>
                                        </div>
                                        <div class="multi-select-option" data-value="strategy_gamma_v1">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">strategy_gamma_v1</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Comparison Cluster</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="tv-cluster-dropdown">
                                    <span class="multi-select-display">Select cluster...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="tv-cluster-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search clusters...">
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="cluster_izmir">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">cluster_izmir</span>
                                        </div>
                                        <div class="multi-select-option" data-value="cluster_kari">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">cluster_kari</span>
                                        </div>
                                        <div class="multi-select-option" data-value="cluster_backtest_v1">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">cluster_backtest_v1</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Segment</label>
                            <select class="form-select" id="tv-segment">
                                <option value="OPTIDX">OPTIDX</option>
                                <option value="OPTIDX_BSE">OPTIDX_BSE</option>
                                <option value="OPTIDX_US">OPTIDX_US</option>
                                <option value="OPTSTK">OPTSTK</option>
                                <option value="FUTSTK">FUTSTK</option>
                            </select>
                        </div>
                    </div>

                    <!-- Second Row: Slave Selection and Actions -->
                    <div class="filter-row">
                        <div class="filter-group slave-group">
                            <label class="form-label">Associated Slaves</label>
                            <div class="multi-select-wrapper">
                                <div class="multi-select-trigger" data-target="tv-slave-dropdown">
                                    <span class="multi-select-display">Select slaves...</span>
                                    <span class="multi-select-count" style="display: none;">0</span>
                                    <i class="fa fa-chevron-down multi-select-arrow"></i>
                                </div>
                                <div class="multi-select-dropdown" id="tv-slave-dropdown">
                                    <div class="multi-select-header">
                                        <input type="text" class="multi-select-search" placeholder="Search slaves...">
                                    </div>
                                    <div class="multi-select-options">
                                        <div class="multi-select-option" data-value="slave_001">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_001</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_002">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_002</span>
                                        </div>
                                        <div class="multi-select-option" data-value="slave_hedge_001">
                                            <input type="checkbox" class="multi-select-checkbox">
                                            <span class="multi-select-label">slave_hedge_001</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="form-label">Exchange</label>
                            <select class="form-select" id="tv-exchange">
                                <option value="IND">IND (India Combined)</option>
                                <option value="NSE">NSE</option>
                                <option value="BSE">BSE</option>
                                <option value="US">US</option>
                            </select>
                        </div>

                        <div class="filter-group actions">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" id="tv-load-data-btn">
                                    <i class="fa fa-database"></i> Load Data
                                </button>
                                <button class="btn btn-primary" id="tv-run-comparison-btn">
                                    <i class="fa fa-play"></i> Run Comparison
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Comparison Results -->
                <div class="content-card">
                    <h4>
                        Strategy vs Cluster Performance Analysis
                        <div class="card-actions">
                            <div class="status-indicator info">
                                <span class="status-dot"></span>
                                Live comparison results
                            </div>
                            <button class="refresh-btn" title="Refresh Comparison">
                                <i class="fa fa-refresh"></i> Refresh
                            </button>
                            <button class="export-btn" title="Export Comparison">
                                <i class="fa fa-download"></i> Export
                            </button>
                        </div>
                    </h4>

                    <!-- Enhanced Summary Metrics -->
                    <div class="metrics-grid">
                        <div class="metric-card positive">
                            <div class="metric-value positive">+127</div>
                            <div class="metric-label">Exit Count Diff (Strategy - Cluster)</div>
                            <div class="metric-change positive">↑ 15% more exits in live strategy</div>
                            <div class="metric-tooltip">?</div>
                        </div>
                        <div class="metric-card positive">
                            <div class="metric-value positive">+₹45,230</div>
                            <div class="metric-label">PnL Diff (Strategy - Cluster)</div>
                            <div class="metric-change positive">↑ 8.2% outperformance vs backtest</div>
                            <div class="metric-tooltip">?</div>
                        </div>
                        <div class="metric-card warning">
                            <div class="metric-value neutral">23</div>
                            <div class="metric-label">Short Duration Trades (Strategy)</div>
                            <div class="metric-change neutral">< 30 sec holding time</div>
                            <div class="metric-tooltip">?</div>
                        </div>
                        <div class="metric-card warning">
                            <div class="metric-value neutral">18</div>
                            <div class="metric-label">Short Duration Trades (Cluster)</div>
                            <div class="metric-change neutral">< 30 sec holding time</div>
                            <div class="metric-tooltip">?</div>
                        </div>
                        <div class="metric-card neutral">
                            <div class="metric-value neutral">2.34s</div>
                            <div class="metric-label">Avg Execution Delay Diff</div>
                            <div class="metric-change negative">↑ Strategy slower than cluster</div>
                            <div class="metric-tooltip">?</div>
                        </div>
                        <div class="metric-card neutral">
                            <div class="metric-value neutral">89.2%</div>
                            <div class="metric-label">Trade Pattern Correlation</div>
                            <div class="metric-change positive">↑ High similarity to backtest</div>
                            <div class="metric-tooltip">?</div>
                        </div>
                    </div>

                    <!-- Enhanced Comparison Charts -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container compact legend-bottom">
                                <div class="chart-header">
                                    <div class="chart-title">Daily Exit Count Comparison</div>
                                    <div class="chart-controls">
                                        <div class="chart-type-toggle">
                                            <button class="chart-type-btn active" data-chart="line">Line</button>
                                            <button class="chart-type-btn" data-chart="bar">Bar</button>
                                        </div>
                                        <div class="chart-legend-controls">
                                            <button class="legend-position-btn active" data-position="bottom">Bottom</button>
                                            <button class="legend-position-btn" data-position="right">Right</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="exit-count-chart" class="loading">
                                    <div class="spinner"></div>
                                    <div class="loading-text">Loading exit count comparison...</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container compact legend-bottom">
                                <div class="chart-header">
                                    <div class="chart-title">Cumulative PnL Comparison</div>
                                    <div class="chart-controls">
                                        <div class="chart-type-toggle">
                                            <button class="chart-type-btn active" data-chart="line">Line</button>
                                            <button class="chart-type-btn" data-chart="area">Area</button>
                                        </div>
                                        <div class="chart-legend-controls">
                                            <button class="legend-position-btn active" data-position="bottom">Bottom</button>
                                            <button class="legend-position-btn" data-position="right">Right</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="pnl-comparison-chart" class="loading">
                                    <div class="spinner"></div>
                                    <div class="loading-text">Loading PnL comparison...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Holding Time Analysis -->
                    <div class="chart-container large legend-right">
                        <div class="chart-header">
                            <div class="chart-title">Holding Time Distribution with KDE Analysis</div>
                            <div class="chart-controls">
                                <div class="chart-type-toggle">
                                    <button class="chart-type-btn active" data-chart="histogram">Histogram</button>
                                    <button class="chart-type-btn" data-chart="kde">KDE</button>
                                    <button class="chart-type-btn" data-chart="combined">Combined</button>
                                </div>
                                <div class="chart-legend-controls">
                                    <span style="font-size: 10px; color: var(--text-muted);">Legend:</span>
                                    <button class="legend-position-btn" data-position="bottom">Bottom</button>
                                    <button class="legend-position-btn active" data-position="right">Right</button>
                                    <button class="legend-position-btn" data-position="top">Top</button>
                                </div>
                            </div>
                        </div>
                        <div id="holding-time-chart" class="loading">
                            <div class="spinner"></div>
                            <div class="loading-text">Loading holding time distribution...</div>
                        </div>
                    </div>

                    <!-- Trade Pattern Analysis -->
                    <div class="collapsible-section">
                        <div class="collapsible-header" onclick="toggleSection(this)">
                            <span><strong>Detailed Trade Pattern Analysis</strong>
                                <span class="status-indicator info">
                                    <span class="status-dot"></span>
                                    Advanced pattern matching
                                </span>
                            </span>
                            <i class="fa fa-chevron-down expand-icon"></i>
                        </div>
                        <div class="collapsible-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container compact">
                                        <div class="chart-header">
                                            <div class="chart-title">Entry/Exit Timing Scatter</div>
                                        </div>
                                        <div id="timing-scatter-chart" class="loading">
                                            <div class="spinner"></div>
                                            <div class="loading-text">Loading timing analysis...</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container compact">
                                        <div class="chart-header">
                                            <div class="chart-title">Trade Size Distribution</div>
                                        </div>
                                        <div id="trade-size-chart" class="loading">
                                            <div class="spinner"></div>
                                            <div class="loading-text">Loading trade size analysis...</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Combined Analytics Panel -->
            <div class="tab-pane fade" id="analytics-panel" role="tabpanel">
                <div class="content-card">
                    <h4>Combined Analytics Dashboard</h4>
                    <p class="text-muted">Integrated view combining slippage analysis with trade comparison metrics</p>
                    
                    <!-- Coming Soon Placeholder -->
                    <div class="text-center py-5">
                        <i class="fa fa-cogs fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Advanced Analytics Coming Soon</h5>
                        <p class="text-muted">This section will provide integrated analysis combining both slippage monitoring and trade comparison features.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.plot.ly/plotly-2.26.0.min.js"></script>
    
    <script>
        // Enhanced functionality for the advanced slippage analysis UI

        // Toggle collapsible sections
        function toggleSection(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('.expand-icon');

            content.classList.toggle('active');
            icon.classList.toggle('rotated');
        }

        // View toggle functionality (Cluster vs Slave)
        document.getElementById('view-toggle-switch').addEventListener('click', function() {
            this.classList.toggle('active');
            const clusterLabel = document.getElementById('cluster-label');
            const slaveLabel = document.getElementById('slave-label');

            if (this.classList.contains('active')) {
                clusterLabel.classList.add('active');
                slaveLabel.classList.remove('active');
                console.log('Switched to Cluster View');
                // Update all charts and tables to show cluster data
                updateViewMode('cluster');
            } else {
                clusterLabel.classList.remove('active');
                slaveLabel.classList.add('active');
                console.log('Switched to Slave View');
                // Update all charts and tables to show slave data
                updateViewMode('slave');
            }
        });

        // Enhanced chart type toggle functionality
        document.querySelectorAll('.chart-type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from siblings
                this.parentElement.querySelectorAll('.chart-type-btn').forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');

                // Update chart based on type
                const chartType = this.dataset.chart;
                const chartContainer = this.closest('.chart-container').querySelector('[id$="-chart"]');
                console.log('Chart type changed to:', chartType, 'for container:', chartContainer.id);
                updateChart(chartContainer.id, chartType);
            });
        });

        // Legend position control functionality
        document.querySelectorAll('.legend-position-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from siblings
                this.parentElement.querySelectorAll('.legend-position-btn').forEach(b => b.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');

                // Update chart container class for legend positioning
                const chartContainer = this.closest('.chart-container');
                const position = this.dataset.position;

                // Remove existing legend position classes
                chartContainer.classList.remove('legend-bottom', 'legend-right', 'legend-top');
                // Add new legend position class
                chartContainer.classList.add(`legend-${position}`);

                // Update the chart with new legend position
                const chartElement = chartContainer.querySelector('[id$="-chart"]');
                if (chartElement && chartElement.data) {
                    updateChartLegendPosition(chartElement.id, position);
                }

                console.log('Legend position changed to:', position, 'for container:', chartElement?.id);
            });
        });

        // Quick date selection
        document.querySelectorAll('.quick-date-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const days = parseInt(this.dataset.days);
                const endDate = new Date();
                const startDate = new Date();
                startDate.setDate(endDate.getDate() - days);

                document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
                document.getElementById('end-date').value = endDate.toISOString().split('T')[0];

                console.log(`Date range set to last ${days} days`);
            });
        });

        // Enhanced Multi-select dropdown functionality
        function initializeMultiSelect() {
            document.querySelectorAll('.multi-select-trigger').forEach(trigger => {
                trigger.addEventListener('click', function() {
                    const dropdownId = this.dataset.target;
                    const dropdown = document.getElementById(dropdownId);
                    const arrow = this.querySelector('.multi-select-arrow');

                    // Close other dropdowns
                    document.querySelectorAll('.multi-select-dropdown.show').forEach(dd => {
                        if (dd.id !== dropdownId) {
                            dd.classList.remove('show');
                            dd.parentElement.querySelector('.multi-select-trigger').classList.remove('open');
                            dd.parentElement.querySelector('.multi-select-arrow').classList.remove('open');
                        }
                    });

                    // Toggle current dropdown
                    dropdown.classList.toggle('show');
                    this.classList.toggle('open');
                    arrow.classList.toggle('open');
                });
            });

            // Handle checkbox selections
            document.querySelectorAll('.multi-select-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    if (e.target.type !== 'checkbox') {
                        const checkbox = this.querySelector('.multi-select-checkbox');
                        checkbox.checked = !checkbox.checked;
                    }

                    this.classList.toggle('selected', this.querySelector('.multi-select-checkbox').checked);
                    updateMultiSelectDisplay(this.closest('.multi-select-dropdown'));
                });
            });

            // Handle search functionality
            document.querySelectorAll('.multi-select-search').forEach(search => {
                search.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const options = this.closest('.multi-select-dropdown').querySelectorAll('.multi-select-option');

                    options.forEach(option => {
                        const text = option.querySelector('.multi-select-label').textContent.toLowerCase();
                        option.style.display = text.includes(searchTerm) ? 'flex' : 'none';
                    });
                });
            });

            // Handle Select All / Clear All
            document.querySelectorAll('.multi-select-action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const dropdown = this.closest('.multi-select-dropdown');
                    const checkboxes = dropdown.querySelectorAll('.multi-select-checkbox');
                    const isSelectAll = this.textContent.includes('Select All');

                    checkboxes.forEach(checkbox => {
                        checkbox.checked = isSelectAll;
                        checkbox.closest('.multi-select-option').classList.toggle('selected', isSelectAll);
                    });

                    updateMultiSelectDisplay(dropdown);
                });
            });
        }

        function updateMultiSelectDisplay(dropdown) {
            const trigger = dropdown.parentElement.querySelector('.multi-select-trigger');
            const display = trigger.querySelector('.multi-select-display');
            const countBadge = trigger.querySelector('.multi-select-count');
            const selectedOptions = dropdown.querySelectorAll('.multi-select-checkbox:checked');

            if (selectedOptions.length === 0) {
                display.textContent = dropdown.id.includes('segment') ? 'Select segments...' :
                                    dropdown.id.includes('exchange') ? 'Select exchanges...' :
                                    dropdown.id.includes('strategy') ? 'Select strategies...' :
                                    'Select slaves...';
                countBadge.style.display = 'none';
            } else if (selectedOptions.length === 1) {
                display.textContent = selectedOptions[0].closest('.multi-select-option').querySelector('.multi-select-label').textContent;
                countBadge.style.display = 'none';
            } else {
                display.textContent = `${selectedOptions.length} selected`;
                countBadge.textContent = selectedOptions.length;
                countBadge.style.display = 'inline-block';
            }
        }

        // Sub-tab functionality with lazy loading
        function initializeSubTabs() {
            document.querySelectorAll('.sub-nav-tabs .nav-link').forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetId = this.dataset.bsTarget.substring(1);
                    const targetPane = document.getElementById(targetId);

                    // Remove active class from all sub-tabs and panes
                    document.querySelectorAll('.sub-nav-tabs .nav-link').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.sub-tab-pane').forEach(p => p.classList.remove('active'));

                    // Add active class to clicked tab and target pane
                    this.classList.add('active');
                    targetPane.classList.add('active');

                    // Lazy load content if needed
                    if (targetPane.querySelector('.sub-tab-loading')) {
                        loadSubTabContent(targetId);
                    }

                    // Try to create charts for specific tabs
                    if (targetId === 'timing-content') {
                        setTimeout(() => {
                            createTimingHistogram();
                        }, 1000);
                    } else if (targetId === 'slave-content') {
                        setTimeout(() => {
                            createSlavePerformanceChart();
                        }, 1000);
                    } else if (targetId === 'strategy-content') {
                        setTimeout(() => {
                            createStrategyComparisonChart();
                        }, 1000);
                    }
                });
            });
        }

        function loadSubTabContent(tabId) {
            const pane = document.getElementById(tabId);
            const loadingDiv = pane.querySelector('.sub-tab-loading');

            if (!loadingDiv) return; // Content already loaded

            // Show loading state
            loadingDiv.innerHTML = `
                <div class="spinner"></div>
                <div class="loading-text">Loading ${tabId.replace('-content', '')} data...</div>
            `;

            // Simulate API call
            setTimeout(() => {
                if (tabId === 'slave-content') {
                    loadingDiv.innerHTML = `
                        <div class="content-card">
                            <h4>Slave-Level Analysis</h4>
                            <div class="chart-container">
                                <div class="chart-header">
                                    <div class="chart-title">Slave Performance Comparison</div>
                                    <div class="chart-controls">
                                        <div class="chart-type-toggle">
                                            <button class="chart-type-btn active" data-chart="bar">Bar</button>
                                            <button class="chart-type-btn" data-chart="line">Line</button>
                                        </div>
                                    </div>
                                </div>
                                <div id="slave-performance-chart"></div>
                            </div>
                            <div class="data-table-container">
                                <div class="data-table-header">
                                    <div class="data-table-title">Slave Performance Summary</div>
                                </div>
                                <div class="data-table">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Slave Name</th>
                                                <th>Total Slippage</th>
                                                <th>Trade Count</th>
                                                <th>Avg Slippage per Trade</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>slave_001</td>
                                                <td class="value-positive">-₹2,450</td>
                                                <td>156</td>
                                                <td class="value-positive">-₹15.7</td>
                                            </tr>
                                            <tr>
                                                <td>slave_002</td>
                                                <td class="value-negative">₹1,230</td>
                                                <td>89</td>
                                                <td class="value-negative">₹13.8</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    `;
                    // Create slave performance chart after DOM update
                    setTimeout(() => {
                        createSlavePerformanceChart();
                    }, 100);
                } else if (tabId === 'timing-content') {
                    loadingDiv.innerHTML = `
                        <div class="content-card">
                            <h4>Timing Analysis</h4>
                            <div class="chart-container">
                                <div class="chart-header">
                                    <div class="chart-title">Execution Timing Distribution</div>
                                </div>
                                <div id="timing-distribution-chart"></div>
                            </div>
                        </div>
                    `;
                    // Create timing distribution chart after DOM update
                    setTimeout(() => {
                        createTimingDistributionChart();
                    }, 100);
                }
            }, 1500);
        }

        function createSlavePerformanceChart() {
            const chartElement = document.getElementById('slave-performance-chart');
            if (!chartElement) {
                console.log('Chart element slave-performance-chart not found');
                return;
            }

            const slaves = ['slave_001', 'slave_002', 'slave_003', 'slave_hedge_001'];
            const slippageValues = [-2450, 1230, -890, -3120];

            const trace = {
                x: slaves,
                y: slippageValues,
                type: 'bar',
                name: 'Total Slippage',
                marker: {
                    color: slippageValues.map(val => val < 0 ? '#28a745' : '#dc3545')
                }
            };

            const layout = {
                title: {
                    text: 'Slave Performance Comparison',
                    font: { size: 16, color: '#2f4251' }
                },
                xaxis: { title: 'Slave Name' },
                yaxis: { title: 'Total Slippage (₹)' },
                plot_bgcolor: 'white',
                paper_bgcolor: 'white',
                margin: { t: 50, r: 20, b: 100, l: 60 },
                autosize: true,
                height: 300
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                displaylogo: false
            };

            try {
                Plotly.newPlot('slave-performance-chart', [trace], layout, config);
                console.log('Slave performance chart created successfully');
            } catch (error) {
                console.error('Error creating slave performance chart:', error);
            }
        }

        function createTimingDistributionChart() {
            const chartElement = document.getElementById('timing-distribution-chart');
            if (!chartElement) {
                console.log('Chart element timing-distribution-chart not found');
                return;
            }

            // Generate realistic execution timing data
            const timingData = [];
            for (let i = 0; i < 500; i++) {
                const timing = Math.exp(Math.random() * 1.5 + 0.3);
                timingData.push(timing);
            }

            const trace = {
                x: timingData,
                type: 'histogram',
                nbinsx: 25,
                name: 'Execution Time',
                marker: { color: '#4d0000', opacity: 0.7 }
            };

            const layout = {
                title: {
                    text: 'Execution Timing Distribution',
                    font: { size: 16, color: '#2f4251' }
                },
                xaxis: { title: 'Execution Time (seconds)' },
                yaxis: { title: 'Frequency' },
                plot_bgcolor: 'white',
                paper_bgcolor: 'white',
                margin: { t: 50, r: 20, b: 50, l: 60 },
                autosize: true,
                height: 300
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                displaylogo: false
            };

            try {
                Plotly.newPlot('timing-distribution-chart', [trace], layout, config);
                console.log('Timing distribution chart created successfully');
            } catch (error) {
                console.error('Error creating timing distribution chart:', error);
            }
        }

        // Enhanced load slaves functionality with strategy-slave mapping
        document.getElementById('load-slaves-btn')?.addEventListener('click', function() {
            loadSlavesForStrategies('main');
        });

        // Trade Vision load data functionality
        document.getElementById('tv-load-data-btn')?.addEventListener('click', function() {
            loadSlavesForStrategies('tv');
        });

        function loadSlavesForStrategies(context) {
            // Get selected strategies from appropriate dropdown
            const strategyDropdownId = context === 'tv' ? 'tv-strategy-dropdown' : 'strategy-dropdown';
            const slaveDropdownId = context === 'tv' ? 'tv-slave-dropdown' : 'slave-dropdown';

            const strategyDropdown = document.getElementById(strategyDropdownId);
            const selectedStrategies = [];

            if (strategyDropdown) {
                const checkedBoxes = strategyDropdown.querySelectorAll('.multi-select-checkbox:checked');
                checkedBoxes.forEach(checkbox => {
                    const option = checkbox.closest('.multi-select-option');
                    if (option) {
                        selectedStrategies.push(option.dataset.value);
                    }
                });
            }

            if (selectedStrategies.length > 0) {
                // Simulate loading slaves with strategy mapping
                showLoadingProgress();
                setTimeout(() => {
                    // Strategy-to-slave mapping
                    const strategySlaveMapping = {
                        'strategy_alpha_v1': ['slave_001', 'slave_002', 'slave_hedge_001'],
                        'strategy_beta_v2': ['slave_002', 'slave_003'],
                        'strategy_gamma_v1': ['slave_001', 'slave_hedge_001', 'slave_hedge_002'],
                        'cluster_izmir': ['slave_001', 'slave_002'],
                        'cluster_kari': ['slave_003', 'slave_hedge_001']
                    };

                    // Get relevant slaves for selected strategies
                    const relevantSlaves = new Set();
                    selectedStrategies.forEach(strategy => {
                        if (strategySlaveMapping[strategy]) {
                            strategySlaveMapping[strategy].forEach(slave => relevantSlaves.add(slave));
                        }
                    });

                    // Populate slave dropdown with relevant slaves
                    const slaveDropdown = document.getElementById(slaveDropdownId);
                    if (slaveDropdown) {
                        const slaveOptions = slaveDropdown.querySelector('.multi-select-options');
                        if (slaveOptions) {
                            // Clear existing selections
                            slaveOptions.querySelectorAll('.multi-select-checkbox').forEach(cb => {
                                cb.checked = false;
                                cb.closest('.multi-select-option').classList.remove('selected');
                            });

                            // Auto-select relevant slaves
                            relevantSlaves.forEach(slaveName => {
                                const slaveOption = slaveOptions.querySelector(`[data-value="${slaveName}"]`);
                                if (slaveOption) {
                                    const checkbox = slaveOption.querySelector('.multi-select-checkbox');
                                    if (checkbox) {
                                        checkbox.checked = true;
                                        slaveOption.classList.add('selected');
                                    }
                                }
                            });

                            updateMultiSelectDisplay(slaveDropdown);
                        }
                    }

                    hideLoadingProgress();
                    showNotification(`${relevantSlaves.size} slaves loaded for ${selectedStrategies.length} strategies`, 'success');
                }, 1500);
            } else {
                showNotification('Please select at least one strategy first', 'warning');
            }
        }

        // Strategy selection change handler to auto-update slave options
        function handleStrategySelectionChange(strategyDropdownId, slaveDropdownId) {
            const strategyDropdown = document.getElementById(strategyDropdownId);
            if (!strategyDropdown) return;

            strategyDropdown.addEventListener('change', function() {
                // Debounce the slave update
                clearTimeout(window.strategyChangeTimeout);
                window.strategyChangeTimeout = setTimeout(() => {
                    updateSlaveOptionsBasedOnStrategies(strategyDropdownId, slaveDropdownId);
                }, 500);
            });
        }

        function updateSlaveOptionsBasedOnStrategies(strategyDropdownId, slaveDropdownId) {
            const strategyDropdown = document.getElementById(strategyDropdownId);
            const slaveDropdown = document.getElementById(slaveDropdownId);

            if (!strategyDropdown || !slaveDropdown) return;

            const selectedStrategies = [];
            const checkedBoxes = strategyDropdown.querySelectorAll('.multi-select-checkbox:checked');
            checkedBoxes.forEach(checkbox => {
                const option = checkbox.closest('.multi-select-option');
                if (option) {
                    selectedStrategies.push(option.dataset.value);
                }
            });

            // Strategy-to-slave mapping
            const strategySlaveMapping = {
                'strategy_alpha_v1': ['slave_001', 'slave_002', 'slave_hedge_001'],
                'strategy_beta_v2': ['slave_002', 'slave_003'],
                'strategy_gamma_v1': ['slave_001', 'slave_hedge_001', 'slave_hedge_002'],
                'cluster_izmir': ['slave_001', 'slave_002'],
                'cluster_kari': ['slave_003', 'slave_hedge_001']
            };

            // Get all available slaves for selected strategies
            const availableSlaves = new Set();
            selectedStrategies.forEach(strategy => {
                if (strategySlaveMapping[strategy]) {
                    strategySlaveMapping[strategy].forEach(slave => availableSlaves.add(slave));
                }
            });

            // Update slave dropdown options visibility/availability
            const slaveOptions = slaveDropdown.querySelectorAll('.multi-select-option');
            slaveOptions.forEach(option => {
                const slaveValue = option.dataset.value;
                if (selectedStrategies.length === 0 || availableSlaves.has(slaveValue)) {
                    option.style.display = 'flex';
                    option.style.opacity = '1';
                } else {
                    option.style.display = 'none';
                    // Uncheck if not available
                    const checkbox = option.querySelector('.multi-select-checkbox');
                    if (checkbox && checkbox.checked) {
                        checkbox.checked = false;
                        option.classList.remove('selected');
                    }
                }
            });

            updateMultiSelectDisplay(slaveDropdown);
        }

        // Enhanced run analysis functionality
        document.getElementById('run-analysis-btn')?.addEventListener('click', function() {
            runAnalysis('main');
        });

        // Trade Vision run comparison functionality
        document.getElementById('tv-run-comparison-btn')?.addEventListener('click', function() {
            runAnalysis('tv');
        });

        function runAnalysis(context) {
            // Validate inputs based on context
            const startDateId = context === 'tv' ? 'tv-start-date' : 'start-date';
            const endDateId = context === 'tv' ? 'tv-end-date' : 'end-date';

            const startDate = document.getElementById(startDateId)?.value;
            const endDate = document.getElementById(endDateId)?.value;

            if (!startDate || !endDate) {
                showNotification('Please select both start and end dates', 'error');
                return;
            }

            if (new Date(startDate) >= new Date(endDate)) {
                showNotification('Start date must be before end date', 'error');
                return;
            }

            // Additional validation for Trade Vision
            if (context === 'tv') {
                const strategyDropdown = document.getElementById('tv-strategy-dropdown');
                const clusterDropdown = document.getElementById('tv-cluster-dropdown');

                const selectedStrategies = strategyDropdown?.querySelectorAll('.multi-select-checkbox:checked').length || 0;
                const selectedClusters = clusterDropdown?.querySelectorAll('.multi-select-checkbox:checked').length || 0;

                if (selectedStrategies === 0) {
                    showNotification('Please select at least one strategy for comparison', 'error');
                    return;
                }

                if (selectedClusters === 0) {
                    showNotification('Please select at least one cluster for comparison', 'error');
                    return;
                }
            }

            // Show loading state for all charts
            showLoadingProgress();
            const loadingSelector = context === 'tv' ? '#comparison-panel .loading' : '.loading';
            document.querySelectorAll(loadingSelector).forEach(loader => {
                loader.innerHTML = `
                    <div class="spinner"></div>
                    <div class="loading-text">${context === 'tv' ? 'Running comparison analysis...' : 'Analyzing data...'}</div>
                    <div class="progress-container">
                        <div class="progress-bar" style="width: 0%;"></div>
                    </div>
                `;
            });

            // Simulate progressive loading
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;

                document.querySelectorAll('.progress-bar').forEach(bar => {
                    bar.style.width = progress + '%';
                });

                if (progress >= 100) {
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        completeAnalysis();
                        hideLoadingProgress();
                        const message = context === 'tv' ? 'Trade Vision comparison completed successfully' : 'Analysis completed successfully';
                        showNotification(message, 'success');
                    }, 500);
                }
            }, 200);
        }

        // Chart zoom controls
        document.querySelectorAll('.zoom-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.title.toLowerCase();
                console.log('Zoom action:', action);
                // Implement zoom functionality here
            });
        });

        // Export functionality
        document.querySelectorAll('.export-btn, .table-action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const exportType = this.textContent.trim().toLowerCase();
                console.log('Export requested:', exportType);
                showNotification(`Exporting ${exportType}...`, 'info');

                // Simulate export
                setTimeout(() => {
                    showNotification(`${exportType} export completed`, 'success');
                }, 1000);
            });
        });

        // Refresh functionality
        document.querySelectorAll('.refresh-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                console.log('Refresh requested');
                showLoadingProgress();
                setTimeout(() => {
                    hideLoadingProgress();
                    showNotification('Data refreshed', 'success');
                    updateTimestamp();
                }, 1500);
            });
        });

        // Helper functions
        function updateViewMode(mode) {
            // Update all charts and tables based on view mode
            console.log('Updating view mode to:', mode);
            // Implementation would update data sources and re-render charts
        }

        function updateChart(chartId, chartType) {
            const chartContainer = document.getElementById(chartId);
            chartContainer.innerHTML = `
                <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #6c757d;">
                    ${chartType.charAt(0).toUpperCase() + chartType.slice(1)} chart would be rendered here with Plotly.js
                </div>
            `;
        }

        function showLoadingProgress() {
            // Show global loading indicator
            console.log('Showing loading progress');
        }

        function hideLoadingProgress() {
            // Hide global loading indicator
            console.log('Hiding loading progress');
        }

        function showNotification(message, type) {
            // Create and show notification
            const notification = document.createElement('div');
            notification.className = `status-indicator ${type}`;
            notification.innerHTML = `<span class="status-dot"></span>${message}`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.padding = '10px 15px';
            notification.style.borderRadius = '6px';
            notification.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        function updateTimestamp() {
            const now = new Date();
            const timestamp = now.toLocaleString('en-IN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const refreshInfo = document.querySelector('.refresh-info .status-indicator');
            if (refreshInfo) {
                refreshInfo.innerHTML = `
                    <span class="status-dot"></span>
                    Last updated: ${timestamp}
                `;
            }
        }

        function completeAnalysis() {
            // Update all charts with completed analysis
            document.querySelectorAll('.loading').forEach(loader => {
                const chartType = loader.id.includes('trend') ? 'trend' :
                                 loader.id.includes('comparison') ? 'comparison' : 'analysis';
                loader.innerHTML = `
                    <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #6c757d; flex-direction: column;">
                        <i class="fa fa-chart-line fa-3x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                        <div>Interactive ${chartType} chart would be rendered here with Plotly.js</div>
                        <div style="font-size: 12px; margin-top: 5px; opacity: 0.7;">Click and drag to zoom, double-click to reset</div>
                    </div>
                `;
            });
        }

        // Column filtering functionality
        function initializeColumnFilters() {
            // Handle filter dropdown toggles
            document.querySelectorAll('.filter-dropdown-trigger').forEach(trigger => {
                trigger.addEventListener('click', function() {
                    const dropdown = this.nextElementSibling;
                    dropdown.classList.toggle('show');
                });
            });

            // Handle filter option selection
            document.querySelectorAll('.filter-option').forEach(option => {
                option.addEventListener('click', function() {
                    const input = this.closest('.column-filter').querySelector('.filter-input');
                    const filterType = this.textContent;

                    // Update input placeholder based on filter type
                    input.placeholder = `${filterType}...`;

                    // Close dropdown
                    this.closest('.filter-dropdown').classList.remove('show');

                    // Apply filter
                    applyColumnFilter(input, filterType);
                });
            });

            // Handle breadcrumb removal
            document.querySelectorAll('.breadcrumb-remove').forEach(remove => {
                remove.addEventListener('click', function() {
                    this.closest('.breadcrumb-item').remove();
                    updateFilterBreadcrumbs();
                });
            });

            // Handle clear all filters
            document.querySelector('.clear-all-filters')?.addEventListener('click', function() {
                document.querySelectorAll('.filter-input').forEach(input => input.value = '');
                document.querySelector('.filter-breadcrumbs').classList.remove('show');
                // Reload table data
                console.log('All filters cleared');
            });

            // Handle column sorting
            document.querySelectorAll('.data-table th').forEach(header => {
                header.addEventListener('click', function() {
                    const sortIcon = this.querySelector('.sort-indicator');
                    if (sortIcon) {
                        // Reset other sort indicators
                        document.querySelectorAll('.sort-indicator').forEach(icon => {
                            if (icon !== sortIcon) {
                                icon.className = 'fa fa-sort sort-indicator';
                            }
                        });

                        // Toggle current sort
                        if (sortIcon.classList.contains('fa-sort')) {
                            sortIcon.className = 'fa fa-sort-asc sort-indicator';
                        } else if (sortIcon.classList.contains('fa-sort-asc')) {
                            sortIcon.className = 'fa fa-sort-desc sort-indicator';
                        } else {
                            sortIcon.className = 'fa fa-sort sort-indicator';
                        }

                        console.log('Sorting column:', this.textContent.trim());
                    }
                });
            });
        }

        function applyColumnFilter(input, filterType) {
            const value = input.value;
            if (value) {
                // Show filter breadcrumbs
                document.querySelector('.filter-breadcrumbs').classList.add('show');
                console.log(`Applied ${filterType} filter: ${value}`);
            }
        }

        function updateFilterBreadcrumbs() {
            const breadcrumbs = document.querySelector('.filter-breadcrumbs');
            const items = breadcrumbs.querySelectorAll('.breadcrumb-item');
            if (items.length === 0) {
                breadcrumbs.classList.remove('show');
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.multi-select-wrapper')) {
                document.querySelectorAll('.multi-select-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                    dropdown.parentElement.querySelector('.multi-select-trigger').classList.remove('open');
                    dropdown.parentElement.querySelector('.multi-select-arrow').classList.remove('open');
                });
            }

            if (!e.target.closest('.column-filter')) {
                document.querySelectorAll('.filter-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });

        // Main tab functionality
        function initializeMainTabs() {
            document.querySelectorAll('#mainTabs .nav-link').forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetId = this.dataset.target;

                    // Remove active class from all main tabs and panels
                    document.querySelectorAll('#mainTabs .nav-link').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-pane').forEach(p => {
                        p.classList.remove('show', 'active');
                        p.style.display = 'none';
                    });

                    // Add active class to clicked tab and show target panel
                    this.classList.add('active');
                    const targetPanel = document.getElementById(targetId);
                    if (targetPanel) {
                        targetPanel.classList.add('show', 'active');
                        targetPanel.style.display = 'block';
                    }
                });
            });
        }

        // Generate realistic slippage data
        function generateSlippageData() {
            const dates = [];
            const slippageValues = [];
            const startDate = new Date('2024-01-01');

            for (let i = 0; i < 30; i++) {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);
                dates.push(date.toISOString().split('T')[0]);

                // Generate realistic slippage values (mostly negative = favorable)
                const baseSlippage = -15 + Math.random() * 30; // Range: -15 to +15 BPS
                slippageValues.push(baseSlippage + Math.sin(i * 0.2) * 5);
            }

            return { dates, slippageValues };
        }

        // Enhanced chart creation with multiple strategies/slaves and proper legend handling
        function createSlippageTrendChart() {
            // Try multiple possible chart container IDs
            let chartElement = document.getElementById('overview-chart');
            if (!chartElement) {
                chartElement = document.getElementById('slippage-trend-chart');
            }
            if (!chartElement) {
                console.log('Chart element not found (tried overview-chart and slippage-trend-chart)');
                return;
            }

            const { dates, slippageValues } = generateSlippageData();

            // Create multiple traces for different strategies/slaves
            const strategies = ['strategy_alpha_v1', 'strategy_beta_v2', 'strategy_gamma_v1'];
            const slaves = ['slave_001', 'slave_002', 'slave_hedge_001'];
            const colors = ['#4d0000', '#c50000', '#0066cc', '#28a745', '#fd7e14', '#6f42c1'];

            const traces = [];

            // Add strategy traces
            strategies.forEach((strategy, index) => {
                const strategyData = slippageValues.map(val => val + (Math.random() - 0.5) * 10);
                traces.push({
                    x: dates,
                    y: strategyData,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: strategy,
                    line: { color: colors[index], width: 2 },
                    marker: { size: 4, color: colors[index] },
                    legendgroup: 'strategies'
                });
            });

            // Add slave traces
            slaves.forEach((slave, index) => {
                const slaveData = slippageValues.map(val => val + (Math.random() - 0.5) * 8);
                traces.push({
                    x: dates,
                    y: slaveData,
                    type: 'scatter',
                    mode: 'lines',
                    name: slave,
                    line: { color: colors[index + 3], width: 1, dash: 'dot' },
                    legendgroup: 'slaves'
                });
            });

            // Determine legend position from container class
            const container = chartElement.closest('.chart-container');
            let legendConfig = { orientation: 'v', x: 1.02, y: 1 }; // default right

            if (container.classList.contains('legend-bottom')) {
                legendConfig = { orientation: 'h', x: 0.5, y: -0.2, xanchor: 'center' };
            } else if (container.classList.contains('legend-top')) {
                legendConfig = { orientation: 'h', x: 0.5, y: 1.1, xanchor: 'center' };
            }

            const layout = {
                title: {
                    text: chartElement.id.includes('overview') ? 'Strategy & Slave Performance Overview' : 'Multi-Strategy Slippage Trend',
                    font: { size: 16, color: '#2f4251' }
                },
                xaxis: {
                    title: 'Date',
                    gridcolor: '#e9ecef'
                },
                yaxis: {
                    title: 'Slippage (BPS)',
                    gridcolor: '#e9ecef',
                    zeroline: true,
                    zerolinecolor: '#6c757d'
                },
                legend: {
                    ...legendConfig,
                    bgcolor: 'rgba(255,255,255,0.8)',
                    bordercolor: '#e9ecef',
                    borderwidth: 1,
                    font: { size: 10 }
                },
                plot_bgcolor: 'white',
                paper_bgcolor: 'white',
                margin: container.classList.contains('legend-bottom') ?
                    { t: 50, r: 20, b: 100, l: 60 } :
                    container.classList.contains('legend-right') ?
                    { t: 50, r: 150, b: 50, l: 60 } :
                    { t: 80, r: 20, b: 50, l: 60 },
                hovermode: 'x unified',
                autosize: true,
                responsive: true
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
                displaylogo: false,
                toImageButtonOptions: {
                    format: 'png',
                    filename: 'slippage_trend',
                    height: container.classList.contains('large') ? 450 : 300,
                    width: 1000,
                    scale: 1
                }
            };

            try {
                Plotly.newPlot(chartElement.id, traces, layout, config);
                console.log(`Enhanced chart with ${traces.length} traces created successfully in ${chartElement.id}`);
            } catch (error) {
                console.error('Error creating enhanced chart:', error);
            }
        }

        // Function to update chart legend position dynamically
        function updateChartLegendPosition(chartId, position) {
            const chartElement = document.getElementById(chartId);
            if (!chartElement || !chartElement.data) return;

            let legendUpdate = { 'legend.orientation': 'v', 'legend.x': 1.02, 'legend.y': 1 };
            let marginUpdate = { 'margin.t': 50, 'margin.r': 150, 'margin.b': 50, 'margin.l': 60 };

            if (position === 'bottom') {
                legendUpdate = {
                    'legend.orientation': 'h',
                    'legend.x': 0.5,
                    'legend.y': -0.2,
                    'legend.xanchor': 'center'
                };
                marginUpdate = { 'margin.t': 50, 'margin.r': 20, 'margin.b': 100, 'margin.l': 60 };
            } else if (position === 'top') {
                legendUpdate = {
                    'legend.orientation': 'h',
                    'legend.x': 0.5,
                    'legend.y': 1.1,
                    'legend.xanchor': 'center'
                };
                marginUpdate = { 'margin.t': 80, 'margin.r': 20, 'margin.b': 50, 'margin.l': 60 };
            }

            try {
                Plotly.relayout(chartId, { ...legendUpdate, ...marginUpdate });
                console.log(`Legend position updated to ${position} for chart ${chartId}`);
            } catch (error) {
                console.error(`Error updating legend position for ${chartId}:`, error);
            }
        }

        function createStrategyComparisonChart() {
            // Wait for the element to be available
            setTimeout(() => {
                const chartElement = document.getElementById('strategy-comparison-chart');
                if (!chartElement) {
                    console.log('Chart element strategy-comparison-chart not found');
                    return;
                }

                const strategies = ['strategy_alpha_v1', 'strategy_beta_v2', 'strategy_gamma_v1', 'cluster_izmir'];
                const totalSlippage = [-12450, 8750, -3200, -15680];
                const executionSlippage = [-8230, 5120, -1850, -12340];

                const trace1 = {
                    x: strategies,
                    y: totalSlippage,
                    type: 'bar',
                    name: 'Total Slippage',
                    marker: { color: '#4d0000' }
                };

                const trace2 = {
                    x: strategies,
                    y: executionSlippage,
                    type: 'bar',
                    name: 'Execution Slippage',
                    marker: { color: '#c50000' }
                };

                const layout = {
                    title: {
                        text: 'Strategy Slippage Comparison',
                        font: { size: 16, color: '#2f4251' }
                    },
                    xaxis: { title: 'Strategy' },
                    yaxis: { title: 'Slippage (₹)' },
                    barmode: 'group',
                    plot_bgcolor: 'white',
                    paper_bgcolor: 'white',
                    margin: { t: 50, r: 20, b: 100, l: 60 },
                    autosize: true,
                    height: 300
                };

                const config = {
                    responsive: true,
                    displayModeBar: true,
                    displaylogo: false
                };

                try {
                    Plotly.newPlot('strategy-comparison-chart', [trace1, trace2], layout, config);
                    console.log('Strategy comparison chart created successfully');
                } catch (error) {
                    console.error('Error creating strategy comparison chart:', error);
                }
            }, 500);
        }

        function createTimingHistogram() {
            // Wait for the element to be available
            setTimeout(() => {
                let chartElement = document.getElementById('timing-histogram-chart');
                if (!chartElement) {
                    chartElement = document.getElementById('slave-comparison-chart');
                }
                if (!chartElement) {
                    console.log('Chart element not found (tried timing-histogram-chart and slave-comparison-chart)');
                    return;
                }

                // Generate realistic execution timing data
                const timingData = [];
                for (let i = 0; i < 1000; i++) {
                    // Log-normal distribution for execution times
                    const timing = Math.exp(Math.random() * 2 + 0.5); // 0.5 to 20 seconds
                    timingData.push(timing);
                }

                const trace = {
                    x: timingData,
                    type: 'histogram',
                    nbinsx: 30,
                    name: 'Execution Time Distribution',
                    marker: { color: '#4d0000', opacity: 0.7 }
                };

                const layout = {
                    title: {
                        text: 'Execution Timing Distribution',
                        font: { size: 16, color: '#2f4251' }
                    },
                    xaxis: { title: 'Execution Time (seconds)' },
                    yaxis: { title: 'Frequency' },
                    plot_bgcolor: 'white',
                    paper_bgcolor: 'white',
                    margin: { t: 50, r: 20, b: 50, l: 60 },
                    autosize: true,
                    height: 300
                };

                const config = {
                    responsive: true,
                    displayModeBar: true,
                    displaylogo: false
                };

                try {
                    Plotly.newPlot(chartElement.id, [trace], layout, config);
                    console.log(`Timing histogram chart created successfully in ${chartElement.id}`);
                } catch (error) {
                    console.error('Error creating timing histogram chart:', error);
                }
            }, 500);
        }

        // Debug function to check available chart containers
        function debugChartContainers() {
            const chartIds = [
                'overview-chart', 'slippage-trend-chart', 'strategy-comparison-chart',
                'slave-comparison-chart', 'timing-histogram-chart', 'slave-performance-chart',
                'timing-distribution-chart', 'exit-count-chart', 'pnl-comparison-chart', 'holding-time-chart'
            ];

            console.log('=== Chart Container Debug ===');
            chartIds.forEach(id => {
                const element = document.getElementById(id);
                console.log(`${id}: ${element ? 'FOUND' : 'NOT FOUND'}`);
            });
            console.log('=== End Debug ===');
        }

        // Create charts for all available containers
        function createAllAvailableCharts() {
            console.log('Creating all available charts...');
            debugChartContainers();

            // Overview/Slippage Trend Chart
            createSlippageTrendChart();

            // Strategy Comparison Chart
            createStrategyComparisonChart();

            // Timing Histogram Chart
            createTimingHistogram();

            // Slave Comparison Chart (in main overview)
            createSlaveComparisonChart();

            // Create simple charts for any available containers
            createSimpleChartsForAvailableContainers();

            // Try to create charts for sub-tabs
            setTimeout(() => {
                createSlavePerformanceChart();
                createTimingDistributionChart();
            }, 1000);
        }

        // Create simple charts for any available containers
        function createSimpleChartsForAvailableContainers() {
            const chartConfigs = [
                { id: 'exit-count-chart', title: 'Daily Exit Count', type: 'line' },
                { id: 'pnl-comparison-chart', title: 'PnL Comparison', type: 'bar' },
                { id: 'holding-time-chart', title: 'Holding Time Distribution', type: 'histogram' }
            ];

            chartConfigs.forEach(config => {
                const element = document.getElementById(config.id);
                if (element) {
                    createSimpleChart(config.id, config.title, config.type);
                }
            });
        }

        // Create a simple chart for any container
        function createSimpleChart(containerId, title, type) {
            const dates = [];
            const values = [];
            for (let i = 0; i < 10; i++) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                dates.push(date.toISOString().split('T')[0]);
                values.push(Math.random() * 100 - 50);
            }

            let trace;
            if (type === 'line') {
                trace = {
                    x: dates,
                    y: values,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: title
                };
            } else if (type === 'bar') {
                trace = {
                    x: ['Strategy A', 'Strategy B', 'Strategy C'],
                    y: [values[0], values[1], values[2]],
                    type: 'bar',
                    name: title
                };
            } else if (type === 'histogram') {
                trace = {
                    x: values,
                    type: 'histogram',
                    name: title
                };
            }

            const layout = {
                title: { text: title, font: { size: 14 } },
                margin: { t: 40, r: 20, b: 40, l: 50 },
                autosize: true,
                height: 250
            };

            const config = {
                responsive: true,
                displayModeBar: false
            };

            try {
                Plotly.newPlot(containerId, [trace], layout, config);
                console.log(`Simple chart created for ${containerId}`);
            } catch (error) {
                console.error(`Error creating simple chart for ${containerId}:`, error);
            }
        }

        // Create slave comparison chart for main overview
        function createSlaveComparisonChart() {
            const chartElement = document.getElementById('slave-comparison-chart');
            if (!chartElement) {
                console.log('Chart element slave-comparison-chart not found');
                return;
            }

            const slaves = ['slave_001', 'slave_002', 'slave_003', 'slave_hedge_001'];
            const slippageValues = [-2450, 1230, -890, -3120];

            const trace = {
                x: slaves,
                y: slippageValues,
                type: 'bar',
                name: 'Total Slippage',
                marker: {
                    color: slippageValues.map(val => val < 0 ? '#28a745' : '#dc3545')
                }
            };

            const layout = {
                title: {
                    text: 'Slave Performance Overview',
                    font: { size: 16, color: '#2f4251' }
                },
                xaxis: { title: 'Slave Name' },
                yaxis: { title: 'Total Slippage (₹)' },
                plot_bgcolor: 'white',
                paper_bgcolor: 'white',
                margin: { t: 50, r: 20, b: 100, l: 60 },
                autosize: true,
                height: 300
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                displaylogo: false
            };

            try {
                Plotly.newPlot('slave-comparison-chart', [trace], layout, config);
                console.log('Slave comparison chart created successfully');
            } catch (error) {
                console.error('Error creating slave comparison chart:', error);
            }
        }

        // Handle window resize for charts
        function handleChartResize() {
            const chartIds = ['overview-chart', 'slippage-trend-chart', 'strategy-comparison-chart', 'slave-performance-chart', 'timing-distribution-chart', 'timing-histogram-chart', 'slave-comparison-chart'];
            chartIds.forEach(chartId => {
                const chartElement = document.getElementById(chartId);
                if (chartElement && chartElement.data) {
                    try {
                        Plotly.Plots.resize(chartElement);
                    } catch (error) {
                        console.warn(`Failed to resize chart ${chartId}:`, error);
                    }
                }
            });
        }

        // Debounced resize handler
        let resizeTimeout;
        function debouncedResize() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(handleChartResize, 250);
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Advanced Slippage Analysis UI initialized');

            // Initialize all components
            initializeMainTabs();
            initializeMultiSelect();
            initializeSubTabs();
            initializeColumnFilters();

            // Initialize strategy-slave dropdown integration
            handleStrategySelectionChange('strategy-dropdown', 'slave-dropdown');
            handleStrategySelectionChange('tv-strategy-dropdown', 'tv-slave-dropdown');

            // Add window resize handler for charts
            window.addEventListener('resize', debouncedResize);

            // Set initial selections for demo
            setTimeout(() => {
                // Set some initial selections
                const optidxCheckbox = document.querySelector('[data-value="OPTIDX"] .multi-select-checkbox');
                const optidxOption = document.querySelector('[data-value="OPTIDX"]');
                const indCheckbox = document.querySelector('[data-value="IND"] .multi-select-checkbox');
                const indOption = document.querySelector('[data-value="IND"]');

                if (optidxCheckbox && optidxOption) {
                    optidxCheckbox.checked = true;
                    optidxOption.classList.add('selected');
                }

                if (indCheckbox && indOption) {
                    indCheckbox.checked = true;
                    indOption.classList.add('selected');
                }

                // Update displays
                document.querySelectorAll('.multi-select-dropdown').forEach(updateMultiSelectDisplay);

                // Show filter breadcrumbs for demo
                const filterBreadcrumbs = document.querySelector('.filter-breadcrumbs');
                if (filterBreadcrumbs) {
                    filterBreadcrumbs.classList.add('show');
                }

                // Create all available charts
                createAllAvailableCharts();

                // Retry chart creation for elements that might not be ready
                setTimeout(() => {
                    createAllAvailableCharts();
                }, 3000);

                // Complete initial analysis
                completeAnalysis();
            }, 1000);
        });
    </script>
</body>
</html>
